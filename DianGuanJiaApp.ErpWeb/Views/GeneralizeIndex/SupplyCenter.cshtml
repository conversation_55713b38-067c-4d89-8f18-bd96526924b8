
@{
    ViewBag.Title = "货源中心";
    ViewBag.MenuId = "SupplyCenter";
    ViewBag.MenuActive = "SupplyCenter";
}
@section Header {
    <link rel="stylesheet" href="~/Content\css\DistributionProduct\SelectionDistribution.css?v=@ViewBag.SystemVersion" />
    <style type="text/css">
        body::-webkit-scrollbar {
            display: none;
        }
        .goods .goods-item {
            background: #fff;
        }
        .screening-footer {
            border-top: none;
            border-radius: 8px;
        }
        .pop-up-layer {
            display: none;
        }
        .positionAddr:hover .pop-up-layer{
            display: block;
        }
        .pop-up-layer{
            top: 31px;
        }
    </style>
}

<div id="selectionDistribution"></div>
@{Html.RenderPartial("~/Views/Components/Popover.cshtml");}

<script src="~/Scripts/react/react.production.min.js"></script>
<script src="~/Scripts/react/react-dom.production.min.js"></script>
<script src="~/Scripts/react/babel.min.js"></script>

<script type="text/babel">
 commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
window.onload= function(){
    class SelectionDistribution extends React.Component {
        constructor() {
            super()
            this.state = {
                isLoading:false,
                // 分类
                categoryList: [

                ],
                // 厂家列表
                supplierList: [
                    { name: "全部", id: 1,value:"",isCheck:true },
                ],
                // 面单列表
                expressBillList: [
                    { name: "不限", id: 1,value:"",isCheck:true },
                    { name: "抖音", id: 2,value: "1" },
                    { name: "拼多多", id: 3,value: "2" },
                    { name: "京东", id: 4,value: "3" },
                    { name: "淘宝", id: 5,value: "4" },
                ],
                // 排序
                sortData: [
                    { name: "上架时间", id: 1,value:"PublicTime",isCheck:true,sort:null},
                    { name: "供货价", id: 2,value: "DistributePrice",isCheck:false,sort:null },
                    // { name: "发货量", id: 3,value: "2" },
                    // { name: "退货率", id: 4,value: "3" },
                ],
                // 表单数据
                formData: {
                    CategoryId: [],
                    SupplierFxUserId: "", // 厂家Id，当Tag=2时必填，Tag=3时选填
                    SupplierFxUserIds: [],
                    faceSheet: "",
                    // 标记，1-我的小站 2-厂商小站 3-选品铺货 , 4-货源中心
                    Tag: 3,
                    //排序字段 上新时间PublicTime 供货价 发货量ShipmentsCount 退货率 RefundRate ,
                    OrderByField: 'PublicTime',
                    IsOrderDesc: true, //是否排序
                    ProductName: '',  // 商品名称
                    ExpressBillId: [],  //面单id
                    PageIndex:1,
					PageSize:50,
                },
                // 列表数据
                list:[],
                listCount:0,
                // 全选
                allCheck:false,
                checkSkuList:[],
                // 每项商品动态宽度
                itemDynamicWidth: 232,
                isLoading: false,
                showOrderTips: false, // 显示订购提示
                subscribeNormalShopCount: 0, // 店铺未订购应用数量
                copyTips:{
                    FailedCount:0,
                    SuccessCount:0,

                },
                isShowCopyTips:false,
                // 半选状态
                partialSelect: false,
                popoverMsg: '',
                shippingWarehouse: [], // 发货仓库
                shippingWarehouseSelectList: [], // 发货仓库已选下拉列表
            }
        }
        componentDidMount() {
            const that = this;
            this.getSupplier();
            this.getExpressBill();
            //this.getCategories();
            const shippingWarehouse = [
                {
                    value: 'all',
                    label: '全选',
                    isActive: false,
                    indeterminate: false, // 半选状态
                },
                {
                    value: '1',
                    label: '深圳仓',
                    isActive: false
                },
                {
                    value: '2',
                    label: '义乌仓',
                    isActive: false
                }
            ]
            this.setState({
                shippingWarehouse
            })
            this.LoadList();
            this.onresizeLoading = true;
            window.onresize = function(event){
                //this.refs.goodsRef.getBoundingClientRect()
                if (that.onresizeLoading) {
                    that.onReSize()
                }
            }
            // 监听搜索回车
            $('#searchInput').keydown(function(e){
                if(e.keyCode ==13){ // 触发键盘事件enter 防止冒泡产生
                    that.onSearch(true);
                }
            });

            window.addEventListener('message', function (e) {
                if (e.data.operateType && e.data.operateType == "closePrepareDistribution") {   //ifarme关闭铺货 传过来的
                    $("#PrepareDistributionIframeWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                        var times = e.data.times || null;
                    if (times) {
                        commonModule.w_alert({ type: 5, skin: 'goBackBatchSetSkin', times: 3000, content: '已取消本次铺货<span class="n-dColor mL12 hover" style="margin-left:12px;" onclick="commonModule.newTarDistribution()">恢复</span>' });
                    }
                }

                // 铺货完成的处理
                if (e.data.operateType && e.data.operateType == "StartDistribution") {   //ifarme关闭铺货 传过来的
                    $("#PrepareDistributionIframeWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                    var resultData=JSON.stringify(e.data.resultData);
                    commonModule.alertDistribution(resultData);
                    that.getSubscribeNormalAppShops(); // 店铺订购普通应用情况, 铺货完成时右上角推出提示
                }
                if (e.data.operateType && e.data.operateType == "BatchStartDistribution") {   //ifarme关闭铺货 传过来的
                    $("#PrepareDistributionIframeWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                    var resultData=JSON.stringify(e.data.resultData);
                    commonModule.alertBatchDistribution(resultData);
                }

                if (e.data.isShowScreen) {
                    $("#PrepareDistributionIframe").addClass('activeScreen').addClass('createProductIframe');

                } else {
                    $("#PrepareDistributionIframe").removeClass('activeScreen').removeClass('createProductIframe');
                }


            })
        }
        // 选择 - 发货仓城市
        onSelectShippingWarehouse(itemData,index) {
            const {shippingWarehouse} = this.state;
            let isCheck = !itemData.isActive;
            let checkSkuList = [];
            shippingWarehouse.forEach((item, i) => {
                if (index == i) {
                    item.isActive = isCheck;
                }
                if (itemData.value == "all") {
                    item.isActive = isCheck;    
                }
                if (item.isActive && item.value != "all") {
                    checkSkuList.push(item.value); 
                } 
            })
            if (checkSkuList.length == 0) {
                shippingWarehouse[0].isActive = false;
            }
            if (checkSkuList.length == shippingWarehouse.length - 1 || checkSkuList.length) {
                shippingWarehouse[0].isActive = true;
            }
            this.setState({
                shippingWarehouse: shippingWarehouse,
                shippingWarehouseSelectList: checkSkuList
            })
        }
        // 同步发货仓城市
        onSyncCity() {
            
        }
        // 添加发货仓城市
        onAddShippingWarehouse() {
            window.open('https://jsls.jinritemai.com/mfa/organization-management/warehouse?btm_pre=a58562.b22739.c305477.d522685', '_blank');
        }
        // 筛选(数组keys，列表项，下标， 表单key)
        onChangeCheck(keyName, item, index,formKey) {
            let { [keyName]: list, formData } = this.state;
            let keys = [];
            list.forEach((iData,i) => {
                if (index) {
                    if(!item.isCheck && i === 0) {
                        iData.isCheck = false;
                    }
                    if (index === i) {
                        iData.isCheck = !iData.isCheck;
                    }
                    if (iData.isCheck) {
                        keys.push(iData.value)
                    }
                } else {
                    iData.isCheck = !item.isCheck;

                }
            })
            formData[formKey] = keys.length ? keys :[];
            if (formData[formKey].length === 0) {
                list[0].isCheck = true;
            }
            this.setState({
                [keyName]: list,
                formData
            },()=>{
                this.LoadList(true)
            })
        }

        onInput(event) {
            let { formData } = this.state;
            formData.ProductName = event.target.value;
            if (event.target.value == '') {
                this.LoadList(true);
            }
            this.setState({
                formData
            })
        }
        onSearch() {
            const {isLoading} = this.state;
            if(isLoading) {
                return false;
            }
            this.LoadList(true);
        }
        // 排序
        onChangeSort(item,index) {
            let { formData,sortData } = this.state;

            let sort = item.sort ? false : true;
            formData.IsOrderDesc = sort;
            formData.OrderByField = item.value;
            //sortData[index].sort = sort;
            sortData.forEach((iData,i)=> {
                if (index == i) {
                    iData.isCheck = true;
                    iData.sort = sort
                } else {
                    iData.isCheck = false;
                    iData.sort = null
                }
            })
            if (item.sort === true && sort == false) {
                sort = null;
                sortData[index].sort = sort;
            }
            this.setState({
                sortData,
                formData
            }, ()=>{
                this.LoadList(true)
            })

        }
        // 屏幕宽度
        onReSize(e) {
            this.onresizeLoading = false
            let DomRect = this.refs.goodsRef.getBoundingClientRect();
            // 主体dom宽度
            let width = parseInt(DomRect.width);
            // 每项宽度
            let itemWidth = 232;
            // 每项间距
            let itemMargin = 16;
            let minCount = 5;
            // 每行显示个数
            let itemCount = Math.floor((width) / (itemWidth + itemMargin));
            itemCount = minCount > itemCount ? minCount : itemCount;
            // 是否满足dom宽度
            let isReplete = width - ((itemWidth + itemMargin) * itemCount);
            // 每项动态宽度
            let itemDynamicWidth = 0;
            if (isReplete === 0) {
                itemDynamicWidth = itemWidth;
            } else {
                itemDynamicWidth = parseInt(width / itemCount) - itemMargin;
            }
            this.setState({
                itemDynamicWidth
            })
            this.onresizeLoading = true;

        }
        // 动态计算dom显示更多
        onBoxSize(ref){
            if (ref) {
                //console.log("onBoxSize",ref.getBoundingClientRect())
               // const dom = expressBillWarp
                let domArr = ref.querySelectorAll('.list-item')
                let array = Array.from(domArr) //
                //console.log("array==",array)
                // 求出元素叠加后的总长度
                // 使用reduce方法计算总和
                let sum = array.reduce((acc, obj) => {
                    return acc + obj.offsetWidth;
                }, 0);
                //console.log("sum ==",sum )
            }

        }
        // 全选商品
        onCheckAll() {
            let { list, allCheck } = this.state;
            allCheck = !allCheck;
            list.forEach((iData, i) => {
                iData.isCheck = allCheck;
            })
            this.setState({ list: list, allCheck,partialSelect:allCheck })
            this.onCheckSkuList();
        }
        // 单个选择商品
        onCheckGoods(item, index) {
            var list = this.state.list;
            var allCheck = true;
            var partialSelect = false;
            list.forEach((iData, i) => {
                if (index == i) {
                    iData.isCheck = !iData.isCheck;
                }
                // 判断是否全选
                if (!iData.isCheck){
                    allCheck = false
                }
                if (iData.isCheck) {
                    partialSelect = true;
                }
            })
            this.setState({ list: list, allCheck: allCheck,partialSelect: partialSelect});
            this.onCheckSkuList();
        }
        // 获取已选商品
        onCheckSkuList() {
            let { list } = this.state;
            var checkSkuList = [];
            list.forEach((iData, i) => {
                if (iData.isCheck) {
                    checkSkuList.push(iData);
                }
            })
            this.setState({ checkSkuList })
            return checkSkuList;
        }
        onClear() {
            this.setState({
                allCheck:false,
                partialSelect:false,
                checkSkuList:[],
            })
        }
        LoadList(isPaging) {
            let {formData,firstLoading} = this.state;
            var that = this;

            if (isPaging) {
                formData.PageIndex = 1;
                this.setState({hideNoDataStatus:true})
            }
            this.setState({
                isLoading: true,
            })
            this.onClear();
            commonModule.Ajax({
                url: '/api/SupplierProduct/GetList',
                loadingMessage: "查询中",
                showMasker: false,
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function (rsp) {
                    if (rsp.Success) {
                        //console.log("rsp.Data.Rows",rsp.Data.PageList)
                        var list = rsp.Data.PageList || [];
                        for (var i = 0; i < list.length; i++) {
                            var obj = list[i];
                            obj.isCheck = false;
                            obj.MainImgUrl = commonModule.newTransformImgSrc(obj.MainImgUrl);
                        }

                        // 首次列表请求分类保存
                        if (that.state.categoryList && that.state.categoryList.length == 0) {
                            let categoryList = rsp.Data.CategoryList || [];
                            categoryList.forEach((item,i)=>{
                                item.isCheck = false;
                                item.value = item.CateId;
                            })
                            categoryList.unshift({ Name: '全部', Id: 'all', value:'',isCheck:true });
                            that.setState({ categoryList: categoryList })
                        }

                        layui.laypage.render({
                            elem: 'paging',
                            count: rsp.Data.Total,
                            limit: formData.PageSize,
                            curr: formData.PageIndex,
                            limits: [50, 100, 200, 300, 400, 500],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                            jump: function (obj, first) {
                                if (!first) {
                                    formData.PageIndex = obj.curr;
                                    formData.PageSize = obj.limit;
                                    that.setState({ formData: formData }, function () {
                                        that.LoadList();
                                    })
                                }
                            }
                        });
                        that.setState({ list: list,isLoading:false,listCount: rsp.Data.Total || 0 },()=>{
                            that.onReSize();
                        })
                    } else {
                        layer.msg(rsp.Message || '失败');
                    }
                }
            })

        }
        goDetail (item) {
            window.open(commonModule.rewriteUrl('/DistributionProduct/DistributionDetailsDyRetail?uid='+item.UidStr+'&FromType=4'), '_blank');
        }
        // 供应商
        getSupplier (item) {
            const that = this;
            commonModule.Ajax({
                url: '/api/SupplierProduct/GetSuppliers',
                showMasker: false,
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        let list = rsp.Data || [];
                        list.unshift({ SupplierName: '全部', SupplierFxUserId: '',value:'',isCheck:true });
                        let _list = [];
                        list.forEach((item,i)=>{
                            if (item.SupplierName) {
                                item.value = item.SupplierUserId;
                                _list.push(item)
                            }
                        })
                        that.setState({ supplierList: _list })
                    } else {
                        layer.msg(rsp.Message || rsp.Data || '失败');
                    }
                }
            })
        }
        // 面单数据
        getExpressBill(){
            let list = commonModule.allExpressBillData;

            list.forEach((item,i)=>{
                item.isCheck = false;
                item.value = item.Id;
            })
            list.unshift({ ExpressBillName: '全部', Id: 'all', value:'',isCheck:true });
            this.setState({expressBillList:list})
        }
        // 获取类目
        getCategories () {
            const that = this;
            let formData = {
                Pid: 0,
                PlatformType: 'Alibaba',
            }
            commonModule.Ajax({
                url: '/api/Listing/GetCategoryList',
                showMasker: false,
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function (rsp) {
                    if (rsp.Success) {
                        let categoryList = rsp.Data || [];
                        categoryList.forEach((item,i)=>{
                            item.isCheck = false;
                            item.value = item.Id;
                        })
                        categoryList.unshift({ Name: '全部', Id: 'all', value:'',isCheck:true });
                        console.log("categoryList===",categoryList)
                        that.setState({ categoryList: categoryList })

                    } else {
                        layer.msg(rsp.Message || '失败');
                    }
                }
            })
        }

        // 复制商品
        getCopyGoods (item,index) {
            const {list,checkSkuList} = this.state;
            const that = this;
            let copyList = [];
            if (item) {
                let obj = {
                    FxUserId: item.SupplierFxUserId,
                    SupplierProductUid: item.UidStr
                }
                copyList.push(obj);
            } else {
                checkSkuList.forEach((item,i)=>{
                    copyList.push({
                        FxUserId: item.SupplierFxUserId,
                        SupplierProductUid: item.UidStr
                    });
                })
            }
            if(copyList.length==0) {
                layer.msg('请选择要复制的商品');
                return false
            }

            commonModule.Ajax({
                url: '/api/SupplierProduct/Copy',
                loadingMessage: "复制中",
                showMasker: false,
                contentType: 'application/json',
                data: JSON.stringify(copyList),
                success: function (rsp) {
                    if (rsp.Success) {

                        let data = rsp.Data ? JSON.parse(rsp.Data): '';
                        if (data) {
                            that.setState({isShowCopyTips:true,copyTips:data})
                        }

                        if (item) {
                            list[index].IsCopy = true;
                            that.setState({ list: list });

                        } else {
                            that.LoadList(true)
                        }
                    } else {
                        layer.msg(rsp.Message || rsp.Data || '失败');
                    }
                }
            })
        }
        // 获取双角色用户铺货平台的店铺订购普通应用的情况
        getSubscribeNormalAppShops() {
            const that = this;
            commonModule.Ajax({
                url: '/api/Common/GetSubscribeNormalAppShops',
                type: 'POST',
                success: function (rsp) {
                    if (rsp.Success) {
                      var data = rsp.Data;
                      var list = data.filter(function(item) {
                         return !item.IsSubscribeNormalApp;
                      });
                      if (list.length > 0) {
                        that.setState({ showOrderTips: true, subscribeNormalShopCount: list.length});
                      }
                    }
                }
            });
        }
        showTarDistribution(item,index,flag){
            const {checkSkuList}= this.state;
            let popoverMsg = '';
            if (!item && !checkSkuList.length){
               layer.msg('请选择商品');
               return
            }
            if (checkSkuList.length > 50) {
                popoverMsg = '批量铺货不得超过 50 条';
                if (flag) {
                    layer.msg('批量铺货不得超过 50 条');
                }
                this.setState({ popoverMsg });
                return
            }
            var edmitBaseproductuid = []; //编辑商品
            if (!item && checkSkuList.length > 1){

                checkSkuList.map((item,index)=>{
                    if (item.UidStr){
                        //FxUserId = item.SupplierFxUserId || '';
                        UidStr = item.UidStr;
                        edmitBaseproductuid.push(item.UidStr);
                    }
                })
                localStorage.setItem('edmitBaseproductuid',JSON.stringify(edmitBaseproductuid));
                 var FromType = 3;
                let obj = {FromType}
                commonModule.tarDistribution('edmit', '',obj);
            } else {
                //var FxUserId = checkSkuList.length ? checkSkuList[0].SupplierFxUserId : (item.SupplierFxUserId || '');
                //var UidStr = checkSkuList.length ? checkSkuList[0].UidStr : (item.UidStr || '');
                //var FromType = 3;
                //let obj = {FxUserId,FromType}
                //commonModule.tarDistribution('edmit', UidStr,obj);

                var FxUserId = checkSkuList.length ? checkSkuList[0].SupplierFxUserId : (item.SupplierFxUserId || '');
                var UidStr = checkSkuList.length ? checkSkuList[0].UidStr : (item.UidStr || '');
                edmitBaseproductuid.push(UidStr);
                localStorage.setItem('edmitBaseproductuid',JSON.stringify(edmitBaseproductuid));
                var FromType = 3;
                let obj = {FxUserId,FromType}
                commonModule.tarDistribution('edmit', '',obj);
            }
        }
        // 更多面单
        onExpressBillBox(list,itemData) {
            if (!list|| list.length == 0) return
            let arr = [];
            let arrExcess = [];

            list.map((item,index)=>{
                let itemDom = <span className={"wu-pintaiIcon wu-small wu-mR4 "+item.ExpressBillType} title={item.ExpressBillName}></span>
                if (index < 7) {
                    arr.push(itemDom)
                } else {
                    arrExcess.push(itemDom)
                }
            })
             if (arrExcess.length) {

                        let dom = <div className="popoverCommon flex">
                            <span className="icon ExpressBill layui-content-li-icons-item iconfont icon-a-chevron-down1x"></span>

                            <div className="popoverCommon-warn flex">
                                {
                                   arrExcess
                                }
                            </div>
                        </div>
                 arr.push(dom)
            }
            return arr
        }
        // 立即订购
		goOrdering = () => {
            const url = "https://fuwu.jinritemai.com/detail?btm_ppre=a0254.b6901.c8954.d4082_2&btm_pre=a0254.b9825.c7579.d6976_1&page_from=1gr0710iq_b9825&pre_show_id=f7cd2d5b-41d3-40c4-a248-a862d79465a3&searchKey=%E5%BA%97%E7%AE%A1%E5%AE%B6&service_id=24069";
            window.open(commonModule.rewriteUrl(url), "_blank");
		}
        // 基础库
        goBaseProduct = () => {
            this.setState({isShowCopyTips:false});
            const url = "/BaseProduct/NewBaseProduct";
            window.open(commonModule.rewriteUrl(url), "_self");
		}
        // 知道了
        closeTips = () => {
           this.setState({ showOrderTips: false });
        }

        CopyText=(ele)=>{
            commonModule.CopyText(ele);
        }

        // 跳转到厂家小站
        goStation (id) {
            const {details} = this.state;
            if (id) {
                window.open(commonModule.rewriteUrl("/GeneralizeIndex/MyStationCard?id=" + id + "&userType=supplier"), '_self');
                return false;
            }
        }

        render() {
            const {categoryList, supplierList, expressBillList, sortData,
            itemDynamicWidth,list,allCheck,listCount, showOrderTips, 
            subscribeNormalShopCount, isShowCopyTips,copyTips,partialSelect,popoverMsg, shippingWarehouse, shippingWarehouseSelectList 
            } = this.state;
            return (
                <div className="react-container" style={{backgroundImage: "url(/Content/images/distribution/back.png)"}}>
                    {
                       showOrderTips? (
                            <div className="order-tips-wrapper">
                               <div className="order-tips-wrapper-header">
                                  <span className="order-tips-wrapper-header-title"><i className="iconfont icon-a-edit1x icon-a-notification-filled1x"></i>
                                     <span className="n-font5">应用订购提示</span>
                                  </span>
                                  <i className="iconfont icon-a-edit1x icon-a-close1x" onClick={this.closeTips}></i>
                               </div>
                               <div className="order-tips-wrapper-content n-font5">
                                  有 <span className="n-sColor">{subscribeNormalShopCount}</span> 个店铺未订购【店管家_分销代发】应用，将导致铺货商品无法推单给厂家代发，请前往订购。
                               </div>
                               <div className="order-tips-wrapper-footer">
                                   <span className="n-mButton n-sActive" onClick={this.closeTips}>知道了</span>
                                   <span className="n-mButton" style={{marginLeft: "12px"}} onClick={this.goOrdering}>立即订购</span>
                               </div>
                            </div>
                       ) : null
                    }
                    {
                       isShowCopyTips? (
                            <div className="order-tips-wrapper" style={{top:'auto',bottom:'24px'}}>
                               <div className="order-tips-wrapper-header">
                                  <span className="order-tips-wrapper-header-title"><i className="iconfont icon-a-edit1x icon-a-notification-filled1x"></i>
                                     <span className="n-font5">已完成复制到基础商品</span>
                                  </span>
                                  <i className="iconfont icon-a-edit1x icon-a-close1x" onClick={()=>this.setState({isShowCopyTips:false})}></i>
                               </div>
                               <div className="order-tips-wrapper-content n-font5" style={{height:'auto',paddingBottom:'0px'}}>
                                复制成功 <span className="n-dColor">{(copyTips && copyTips.SuccessCount) || 0}</span> 复制失败 <span className="n-sColor">{(copyTips &&copyTips.FailedCount) || 0}</span>
                               </div>
                               <div className="order-tips-wrapper-footer">
                                   <div></div>
                                   <span className="n-mButton" style={{marginLeft: "12px"}} onClick={this.goBaseProduct}>前往基础商品</span>
                               </div>
                            </div>
                       ) : null
                    }
                    <div className="react-container-header flex">
                        <div className="react-container-header-img-l" style={{backgroundImage: "url(/Content/images/distribution/headerBackLeft20240626.png)"}}></div>
                        <div className="react-container-header-img" style={{backgroundImage: "url(/Content/images/distribution/headerBack20240626.png)"}}>

                        </div>
                        <div className="react-container-header-img-r" style={{backgroundImage: "url(/Content/images/distribution/headerBackRight20240626.png)"}}></div>
                    </div>
                    <div className="screening">

                        <div className="screening-cont" style={{display: 'none'}}>

                            <div className="screening-cont-item flex">
                                <div className="screening-cont-item-title">商品类目</div>
                                <ul className="flex isMore" >
                                    {
                                        categoryList.length && categoryList.map((item, index) => {
                                            return (
                                                <li className={item.isCheck ? "active hover" : "hover"} key={index} onClick={() => {this.onChangeCheck('categoryList',item, index,'CategoryId')}}>
                                                    {item.Name}
                                                    {
                                                        item.isCheck ? <div className="s-box"><i className="iconfont icon-dagou"></i></div> : null
                                                    }
                                                </li>
                                            )
                                        })
                                    }
                                </ul>
                            </div>
                            <div className="screening-cont-item flex">
                                <div className="screening-cont-item-title">供货厂家</div>
                                <ul className="flex isMore" ref="screeningUl">
                                    {
                                        supplierList.length && supplierList.map((item, index) => {
                                            return (
                                                <li className={item.isCheck ? "active hover" : "hover"} key={index} onClick={() => {this.onChangeCheck('supplierList',item, index,'SupplierFxUserIds')}}>
                                                    {item.SupplierName}
                                                    {
                                                        item.isCheck ? <div className="s-box"><i className="iconfont icon-dagou"></i></div> : null
                                                    }
                                                </li>
                                            )
                                        })
                                    }
                                </ul>
                            </div>
                            <div className="screening-cont-item flex">
                                <div className="screening-cont-item-title" >面单支持</div>
                                <ul className="flex isMore" ref={(ref)=>{this.onBoxSize(ref, 'expressBillWarp')}}>
                                    {
                                        expressBillList.length&& expressBillList.map((item, index) => {
                                            return (
                                                <li className={item.isCheck ? "active hover list-item" : "hover list-item"} key={index} onClick={() => {this.onChangeCheck('expressBillList',item, index,'ExpressBillId')}}>
                                                    {item.ExpressBillName}
                                                    {
                                                        item.isCheck ? <div className="s-box"><i className="iconfont icon-dagou"></i></div> : null
                                                    }
                                                </li>
                                            )
                                        })
                                    }
                                </ul>
                            </div>

                        </div>

                    </div>
                    <div className="screening-footer flex">
                            <div className="left flex">
                                
                                <div className="wu-btn wu-btn-mid wu-primary wu-two wu-icon-right positionAddr" >
                                    <div onClick={()=>this.getCopyGoods()}>
                                        <i className="iconfont icon-a-location1x wu-btn-icon"></i>
                                        <span>未获取到地址</span>
                                        <i className="iconfont wu-btn-icon icon-a-chevron-down1x wu-btn-icon-b"></i>
                                    </div>
                                    <div className="pop-up-layer">
                                        <div className="pop-up-layer-cont wu-shadow-one wu-6radius wu-p8">
                                            <div className="cont-ul wu-f14 wu-c09">
                                                
                                                <div className="cont-ul-li wu-my-checkboxWrap checked">
                                                    <span className="wu-mL8">上海市（当前定位）</span>
                                                </div>
                                                {
                                                    shippingWarehouse.length ? 
                                                        <div className="cont-ul-li wu-f14 wu-c04">
                                                            <span className="">发货仓城市</span>
                                                        </div>
                                                    : 
                                                    <div className="cont-ul-li" onClick={()=>this.onAddShippingWarehouse()}>
                                                        <i className="iconfont icon-a-add1x"></i>
                                                        <span className="wu-mL8">添加发货仓城市</span>
                                                    </div>
                                                }
                                                {
                                                    shippingWarehouse.map((item,index) => {
                                                        return (
                                                            <div 
                                                                className={"cont-ul-li wu-my-checkboxWrap"+ (item.isActive ? (item.value == 'all' && shippingWarehouseSelectList.length && (shippingWarehouse.length - 1) !== shippingWarehouseSelectList.length) ? " part_checked" : " checked" : "")}
                                                                onClick={() => this.onSelectShippingWarehouse(item,index)}
                                                            >
                                                                <span className="wu-my-checkbox"></span>
                                                                <span className="wu-mL8">{item.label}</span>
                                                            </div>
                                                        )
                                                    })
                                                }
                                                
                                            </div>
                                            <div className="sync-city" onClick={()=>this.onSyncCity()}>
                                                <i className="iconfont icon-tongbu"></i>
                                                <span className="wu-mL8">同步发货仓城市</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="right flex">
                                <div className="sort flex">
                                    {
                                        sortData.map((item, index) => {
                                            return (
                                                <div className={item.isCheck ? "n-mButton n-sActive active hover mR8 flex" : "n-mButton n-sActive hover mR8 flex"} key={"sortData"+index} onClick={() => {this.onChangeSort(item, index)}}>
                                                    {item.name}
                                                    <div className="icon-box " >
                                                        <i className="iconfont icon-xiangxia" x-placement={item.sort ? "up" :  '' }></i>
                                                        <i className="iconfont icon-xiangxia" x-placement={!item.sort && item.sort != null ? 'down': '' }></i>
                                                    </div>
                                                </div>
                                            )
                                        })
                                    }
                                </div>
                                <div className="search flex">
                                    <input type="text" placeholder="请输入商品名称" id="searchInput" className="layui-input n-layui-input wu-6radius" onInput={(e)=>{this.onInput(e)}}/>
                                    <i className="iconfont icon-sousuo hover" onClick={()=>{this.onSearch(true);}}></i>
                                </div>
                            </div>
                        </div>

                    <div className="content">
                        <div className="goods flex" ref="goodsRef" style={{paddingBottom: listCount>50?'40px':'0'}}>
                            {
                                list.length ? list.map((item, index) => {
                                    return (
                                        <div className={item.isCheck ? 'goods-item goods-item-hover hover': 'goods-item hover'} key={"goodsData"+index} style={{width: itemDynamicWidth+"px"}} onClick={()=>this.goDetail(item)}>
                                            <div className="goods-img" style={{width: (itemDynamicWidth - 2) + "px",height: itemDynamicWidth+"px"}}>
                                                
                                                <span style={{display:"none"}} className={item.isCheck ? 'n-newCheckbox c-chx activeF': 'n-newCheckbox c-chx'} onClick={(e)=>{e.stopPropagation(); this.onCheckGoods(item, index)}}></span>
                                                
                                                {
                                                    item.MainImgUrl ?
                                                        <div className="img" style={{backgroundImage: "url("+item.MainImgUrl+")"}}></div>
                                                    : <div className="img noImg"><div className="imgbox" style={{backgroundImage: "url(/Content/images/distribution/icon-zhanweitu240820.png)"}} ></div></div>
                                                }
                                            </div>
                                            <div className="goods-info">
                                                <div className="goods-name ellipsis mB8" title={item.Subject}>{item.Subject}</div>
                                                {
                                                    item.MinPrice == item.MaxPrice ?
                                                        !item.MinPrice && item.MinPrice != 0 ? <div className="goods-price mB8">咨询报价</div>
                                                        :<div className="goods-price mB8">￥<span className="">{item.MinPrice || '0'}</span></div>
                                                        :<div className="goods-price mB8">￥<span className="">{item.MinPrice || '0'}-{item.MaxPrice || '0'}</span></div>
                                                }
                                                <div className="goods-box mB8 ellipsis">
                                                    <div className="goods-num flex" style={{display:"none"}}>
                                                        <div className="c04 mB4"> 发货量 <span className="c09">{item.ShipmentsCount||'--' }</span></div>
                                                        <div className="c04"> 退费率 <span className="c09">{item.ReturnsCount||'--' }</span></div>
                                                    </div>
                                                    <div className="goods-num flex" style={{justifyContent:"flex-start",display:"none"}}>
                                                        <div className="c04 mR4">支持面单</div>
                                                        <div className="expressBillBox flex"> {item.ExpressBill.length ? this.onExpressBillBox(item.ExpressBill,item) :'--'}</div>
                                                    </div>
                                                    <div className="goods-num flex">
                                                        <div className="wu-f12 wu-c04 mR4">发货仓:</div>
                                                        <div className="wu-f12 wu-c09 ellipsis"> {'深圳市福田区国企文化大厦深圳市福田区国企文化大厦'}</div>
                                                    </div>
                                                </div>
                                                <div className="goods-btn flex">
                                                    <div className="n-mButton wu-btn wu-btn-mid"  onClick={(e)=>{e.stopPropagation(); this.showTarDistribution(item, index)}}>立即铺货</div>
                                                    {
                                                        (index % 2) == 0 ?
                                                        <div className="n-mButton wu-btn wu-btn-mid wu-primary wu-two"  onClick={(e)=>{e.stopPropagation(); this.showTarDistribution(item, index)}}>申请合作</div>
                                                        : (index % 3) == 0 ?
                                                        <div className="n-mButton wu-btn wu-btn-mid wu-primary wu-four"  onClick={(e)=>{e.stopPropagation(); this.showTarDistribution(item, index)}}>申请中</div>
                                                        : <div className="n-mButton wu-btn wu-btn-mid wu-primary wu-five"  onClick={(e)=>{e.stopPropagation(); this.showTarDistribution(item, index)}}>已合作</div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })
                                : <div className="noData">
                                    <img className="noData-img" src="/Content/images/distribution/empty0710.png" alt="" />
                                    <div className="noData-btn n-mButton" onClick={()=>this.CopyText('#copyUrl_cj')}>邀请厂家丰富商品</div>
                                    <span style={{display:'none'}} id="copyUrl_cj">https://img.dgjapp.com/fendan/fx_h5_introduce03.png?快来店管家分销小站发布私域货源吧</span>
                                </div>
                            }

                        </div>
                        <div className="n-footerWrap" id="cardProducFooter" style={listCount>50?{display: "flex"}:{display: "none"}}>
                            <div className="n-footerWrap-left">
                               
                            </div>
                            <div className="n-footerWrap-right">

                                <div className="layui-myPage wu-commonPage wu-one" id="paging"></div>

                            </div>
                        </div>
                    </div>
                </div>
            )
        }
     }
     ReactDOM.render(<SelectionDistribution />, document.getElementById("selectionDistribution"));
}
</script>



