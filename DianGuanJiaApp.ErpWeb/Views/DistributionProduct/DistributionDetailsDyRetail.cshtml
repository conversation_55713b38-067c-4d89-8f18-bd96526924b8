@{
	ViewBag.Title = "商品详情页";
}
@section Header {
	<link rel="stylesheet" href="~/Content\css\DistributionProduct\DistributionDetails.css?v=@ViewBag.SystemVersion" />
	<style type="text/css">
		#distributionDetails {
			background: rgba(255, 255, 255, 0.5);
		}
		.react-container {
			background: none;
		}
		.react-container .goodsInfo {
			background: none;
		}
		.react-container .details .cont .cont-l {
			background: none;
			border: none;
		}
	</style>
}

<div id="distributionDetails"></div>
@{
	Html.RenderPartial("~/Views/Components/Popover.cshtml");
}

<script src="~/Scripts/react/react.production.min.js"></script>
<script src="~/Scripts/react/react-dom.production.min.js"></script>
<script src="~/Scripts/react/babel.min.js"></script>
<script>
	commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
	$(function () {
	    if ($('#newtopBanner').is(":hidden")) {
	        $('#LAY_app_body ').css('cssText', 'margin: 0px !important');
		} else {
			$('#LAY_app_body ').css('cssText', 'margin: 50px 0px 0px 0px !important');
		}
	})
</script>
<script type="text/babel">

	class DistributionDetails extends React.Component {
		constructor() {
			super()
			this.state = {
				isLoading: false,
				skuList: [],
				theadData: [],
				// 表单数据
				formData: {
					uid: '',
				},
				details: {},
				dialogData: {
					isSave: false,
					main: null,
				},
				tag: '3',
				userType: '',
				ShipFeeTemplateData: null,
				FromType:0,
			}
		}
		componentDidMount() {
			const that = this;
			let formData = this.state.formData;
			formData.uid = commonModule.getQueryVariable('uid') || '';
			let userType = commonModule.getQueryVariable('userType') || '';
			let tag = commonModule.getQueryVariable('tag') || '4';
			let FromType = commonModule.getQueryVariable('FromType') || 2;
			if (userType == 'supplier') {
				FromType = 3
			}
			this.setState({
				formData: formData,
				tag: tag,
				userType:userType,
				FromType:FromType
			}, () => {
				that.getData();
			})
			window.addEventListener('message', function (e) {
				if (e.data.operateType && e.data.operateType == "closeCretaeProduct") {   //ifarme创建商品和编辑商品完成 传过来的
					$("#businessCardCreateProductIframeWrap").removeClass('active');
					$('body').css({ overflow: 'auto' });
				}
			})
			window.addEventListener('message', function (e) {
				if (e.data.operateType && e.data.operateType == "cretaeProduct") {   //ifarme创建商品和编辑商品完成 传过来的
					commonModule.w_alert({ type: 4, content: e.data.content });
					$("#businessCardCreateProductIframeWrap").removeClass('active');
					$('body').css({ overflow: 'auto' });
					//$('body').css({ overflow: 'auto' });
					setTimeout(function () {
						window.location.href = window.location.href;
					}, 1000)
				}
			})

            window.addEventListener('message', function (e) {
                if (e.data.operateType && e.data.operateType == "closePrepareDistribution") {   //ifarme关闭铺货 传过来的
					var times = e.data.times || null;
					if (times) {
						commonModule.w_alert({ type: 5, skin: 'goBackBatchSetSkin', times: 3000, content: '已取消本次铺货<span class="n-dColor mL12 hover" style="margin-left:12px;" onclick="commonModule.newTarDistribution()">恢复</span>' });
					}
                    $("#PrepareDistributionIframeWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                }
            })

            //铺货完成的处理
            window.addEventListener('message', function (e) {
                if (e.data.operateType && e.data.operateType == "StartDistribution") {   //ifarme关闭铺货 传过来的
                    $("#PrepareDistributionIframeWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                    var resultData=JSON.stringify(e.data.resultData);
                    commonModule.alertDistribution(resultData);
                }
				if (e.data.isShowScreen) {
					$("#PrepareDistributionIframe").addClass('activeScreen').addClass('createProductIframe');

				} else {
					$("#PrepareDistributionIframe").removeClass('activeScreen').removeClass('createProductIframe');
				}
            })
			// 点击document
			$(document).on("click", function (event) {
				let ids = ['moreFreightWarp', 'moreDeliveryAddressTips', 'AfterSalePromiseTips', 'moreAftersaleaddressTips','CompanyInfo'];
				for (let index = 0; index < ids.length; index++) {
					const element = ids[index];
					if ($('#'+element)[0] && !$('#'+element)[0].contains(event.target)) {
						$('#' + element).css("display", "none");
					}
				}

			})
			layui.use('msg', function() {
                var msg = layui.msg;
                msg.render();
            });
		}
		// 获取数据
		getData() {
			const that = this;
			let formData = this.state.formData;
			function onTextFormat(str) {
				return str ? str + ',' : ''
			}
			commonModule.Ajax({
				type: "GET",
				url: "/api/SupplierProduct/GetByUid",
				data: formData,
				loadingMessage: "加载中..",
				success: function (rsp) {
					if (rsp.Success) {
						let details = rsp.Data;

						let skuData = [];
						// 规格格式处理
						if (details.ProductSkus) {
							details.ProductSkus.forEach((item, index) => {
								if (item.Attributes) {
									let arr = JSON.parse(item.Attributes);
									let _arr = [{
										k: '采购价',
										v: item.DistributePrice,
										align: 'right'
									},
									{
										k: 'SKU编码',
										v: item.SkuCode,
										align: 'left'
									},
									]
									arr = arr.concat(_arr)
									skuData.push(arr);
								}
							})
						}
						details.ProductImages.length && details.ProductImages.forEach((item, index) => {
							if (item.ImageUrl) {
								item.ImageUrl = commonModule.newTransformImgSrc(item.ImageUrl);
							}
						})
						that.onColumnSkuFormat(skuData);
						// 商品属性
						details.CategoryAttribute = JSON.parse(details.CategoryAttribute);

						if (details.CategoryAttribute) {
							details.CategoryAttribute.forEach((item, index) => {
								if ((item.FieldType == 'checkbox' || item.FieldType=="checkboxlist") && item.Value.length) {
									item.valueStr = item.Value.join()
								}
							})
						}
						// 商品描述
						let DescriptionStr = [];
						details.DescriptionStr && details.DescriptionStr.forEach((item, index) => {
							item = commonModule.newTransformImgSrc(item);
							DescriptionStr.push(item)
						})
						details.DescriptionStr = DescriptionStr;
						//发货仓地址处理
						if (details.ShipmentsInfo && details.ShipmentsInfo.SendaddressList && details.ShipmentsInfo.SendaddressList.length) {
							let _data = details.ShipmentsInfo.SendaddressList[0];
							details.ShipmentsInfo.SendaddressListStr = onTextFormat(_data.ReceiverName) + onTextFormat(_data.ReceiverContract) + onTextFormat(_data.Province) + onTextFormat(_data.City) + onTextFormat(_data.County) + onTextFormat(_data.Address)
						}

						//售后地址处理
						if (details.AfterSalesInfo && details.AfterSalesInfo.AftersaleaddressList && details.AfterSalesInfo.AftersaleaddressList.length) {
							let _data = details.AfterSalesInfo.AftersaleaddressList[0];
							details.AfterSalesInfo.AftersaleaddressListStr = onTextFormat(_data.ReceiverName) + onTextFormat(_data.ReceiverContract) + onTextFormat(_data.Province) + onTextFormat(_data.City) + onTextFormat(_data.County) + onTextFormat(_data.Address)
						}

						if (details.CompanyInfo && details.CompanyInfo.length) {

							let object = details.CompanyInfo[0];
							let flag = true;
							for (const key in object) {
								if (Object.prototype.hasOwnProperty.call(object, key)) {
									const element = object[key];
									if (!element && key != "BusinessTerm" && key != 'IsLongTerm') {
										flag = false;
									}
								}
							}
							if (!object['IsLongTerm'] && !object['BusinessTerm'] ) {
								flag = false;
							}
							details.CompanyInfoPerfectState = flag;
						} else {
							details.CompanyInfoPerfectState = false;
						}
						// console.log("details.CompanyInfoPerfectState==",details.CompanyInfoPerfectState)
						onTextFormat = null;
						if(skuData){
							skuData.forEach((item,index)=>{
								 if(item){item.forEach(item2=>{
										if(item2 && item2.v && item2.v == "无规格"){
											item2.v = null
										}
									})
								 }
							})
						}
						that.setState({
							details: details,
							skuList: skuData
						}, () => {
							that.listenImgEvent();
						})
						that.getShippingFeeTemplate();
					} else {
						layer.msg(rsp.Message || '失败')
					}
				},

			});
		}

		// 规格格式化
		onColumnSkuFormat(skuData) {
			var skuCombination = '';
			var theadData = [];
			var skuList = [];
			if (skuData.length) {
				for (var i = 0; i < skuData.length; i++) {
					let item = skuData[i];
					let arr = [];
					for (var j = 0; j < item.length; j++) {
						let attributesItem = item[j];

						if (attributesItem.k && i == 0) {
							theadData.push({
								value: attributesItem.k,
								minWidth: 100,
								textAlign: attributesItem.align || 'left'
							})
						}

					}
					skuList.push(arr)
				}
			}
			this.setState({
				theadData
			})
		}
		// 跟据规格遍历排序
		skuForeach(list, skuList) {
			let newList = [];
			let arr = [];
			for (var i = 0; i < list.length; i++) {
				if (Math.ceil((i % skuList.length)) === 0 && i) {
					newList.push(arr);
					arr = [];
				}
				arr.push(list[i])
			}
			newList.push(arr);
			return newList
		}
		listenImgEvent() { //绑定图片事件
			//实现前进和后退小图片的效果=====
			let prev = document.querySelector('.prev');
			let next = document.querySelector('.next');
			let ul = document.querySelector('.spec-items ul');
			let lis = document.querySelectorAll('.spec-items ul li');
			let imgLengh = lis.length;
			let stepNum = 0;
			if(imgLengh==0) return
			if (imgLengh > 5) {
				if (imgLengh > 5) {
					document.getElementById('img_next').classList.add("active")
				}

				prev.onclick = function () {
					if (stepNum > 0) {
						stepNum--;
						ul.style.left = -50 * stepNum + "px";
					}
					if (stepNum == 0) {
						document.getElementById('img_prev').classList.remove("active");
						document.getElementById('img_next').classList.add("active")

					}
				}
				next.onclick = function () {
					if (imgLengh - 5 > stepNum) {
						stepNum++;
						ul.style.left = -50 * stepNum + "px";
						document.getElementById('img_prev').classList.add("active")
					}

					if (stepNum + 5 == imgLengh) {
						document.getElementById('img_next').classList.remove("active")

					}
				}

			}



			/**
			 * 可以有过渡效果：
			 * 1、数值类的
			 * 2、颜色类的
			 * 3、转换：平移、旋转、缩放、倾斜
			 * 盒子阴影
			 */
			//实现鼠标放在小图上，边框变红并且上面显示小图

			let img = document.querySelector('.main-img  img');
			let imgs = document.querySelectorAll('.spec-items img');
			let activeSrc = document.querySelector('#spec_img_ul .img-hover img').src;
			for (let i = 0; i < lis.length; i++) {
				lis[i].onmouseover = function () {
					for (let j = 0; j < lis.length; j++) {
						lis[j].className = 'null';
					}
					lis[i].className = 'img-hover';
					//两种表达：
					// 1、img.src=lis[i].children[0].src;
					//2、img.src=imgs[i].src;
					img.src = imgs[i].src;
					activeSrc = imgs[i].src;

				}
			}


			//实现鼠标滑入显示放大镜和大图的效果
			let mainImg = document.querySelector('.main-img'); //获取中图
			let zoomPup = document.querySelector('.zoom-pup'); //获取放大镜，中图上面的小黄块
			let zoomDiv = document.querySelector('.zoom-div'); //获取大图的div
			let bigImg = document.querySelector('.zoom-div img') //获取大图

			// bigImg.src = document.querySelector('#spec_img_ul .img-hover img').src;

			mainImg.onmouseover = function () {
				zoomPup.style.display = 'block';
				zoomDiv.style.display = 'block';
				bigImg.src = activeSrc;
			}
			//实现鼠标滑出隐藏放大镜和大图的效果
			mainImg.onmouseout = function () {
				zoomPup.style.display = 'none';
				zoomDiv.style.display = 'none';
			}

			//给中图绑定鼠标移动事件
			mainImg.onmousemove = function (e) {
				//获取鼠标距离文档顶部(body)的距离pageX
				let pageY = e.pageY;
				//获取中图距离文档顶部(body)的距离  offsetTop
				let offsetTop = mainImg.offsetTop;
				//获取黄色小块的高度的一半
				let h = zoomPup.clientHeight / 2;
				//获取黄色小块顶部距离中图顶部的距离
				let top = pageY - offsetTop - h;
				//判断top
				if (top <= 0) {
					top = 0;
				} else if (top >= mainImg.clientHeight - zoomPup.clientHeight) {
					top = mainImg.clientHeight - zoomPup.clientHeight;
				}
				//将放大镜的定位top设置为黄色小块顶部距离中图顶部的距离。
				zoomPup.style.top = top + 'px';
				//获取鼠标距离文档左边(body)的距离 pageX
				let pageX = e.pageX;
				//获取中图距离文档左边(body)的距离
				let offsetLeft = mainImg.offsetLeft;
				//获取黄色小块宽度的一半
				let w = zoomPup.clientWidth / 2;
				//获取黄色小块距离中图左边的距离
				let left = pageX - offsetLeft - w;
				//判断left
				if (left <= 0) {
					left = 0;
				} else if (left >= mainImg.clientWidth - zoomPup.clientWidth) {
					left = mainImg.clientWidth - zoomPup.clientWidth;
				}
				//将放大镜的定位left设置为黄色小块顶部距离中图顶部的距离。
				zoomPup.style.left = left + 'px';


				let y = top / (mainImg.clientHeight - zoomPup.clientHeight);
				let yy = y * (800 - 540);
				bigImg.style.top = -yy + 'px'; //让大图向上走yy的距离
				let x = left / (mainImg.clientWidth - zoomPup.clientWidth);
				let xx = x * (800 - 540);
				bigImg.style.left = -xx + 'px'; //让大图向左走xx的距离


			}

		}
		// 表格合并处理
		onFormatTbody() {
			const { skuList } = this.state;
			let keyName = ''
			var maxCol = 2, val, count, start;
			for (var col = maxCol - 1; col >= 0; col--) {
				count = 1;
				val = "";
				for (var i = 0; i < skuList.length; i++) {
					if (val == skuList[i][col].v) {
						count++;
					} else {
						if (count > 1) { //合并
							start = i - count;
							skuList[start][col].rowSpan = count;
							for (var j = start + 1; j < i; j++) {
								skuList[j][col].v = "none";
							}
							count = 1;
						}
						val = skuList[i][col].v;
					}
				}
				if (count > 1) { //合并，最后几行相同的情况下
					start = i - count;
					skuList[start][col].rowSpan = count;
					for (var j = start + 1; j < i; j++) {
						skuList[j][col].v = "none";
					}
				}
			}
			let tableTrDom = [];
			skuList.map((item, i) => {
				if (i == 0) {
					keyName = item[0]
				}
				let itemData = <tr>
					{
						item.map((jData, j) => {
							return (
								<td style={{ minWidth: '100px', display: jData.v == 'none' ? 'none' : 'table-cell' }} className="" rowspan={jData.rowSpan}>
									<div style={{ textAlign: jData.align || 'left' }}>
										<span>{jData.v}</span>
									</div>
								</td>
							)
						})
					}

				</tr>
				tableTrDom.push(itemData)
			})
			return tableTrDom
		}
		onToggle(id) {
			let ids = ['moreFreightWarp', 'moreDeliveryAddressTips', 'AfterSalePromiseTips', 'moreAftersaleaddressTips','CompanyInfo'];
			for (let index = 0; index < ids.length; index++) {
				const element = ids[index];
				if (id != element) {
					$('#' + element).css("display", "none");
				}
			}
			if (id) {
				// console.log("id===",id)
				var $deliveryEl = $('#' + id);
			    $deliveryEl.slideToggle();
			}

		}

		onBusinessCard() {
			var that = this;
			var dialogData = this.state.dialogData;
			dialogData.main = layer.open({
				type: 1,
				title: '业务联系人',
				content: $('#dialogBusinessCard'),
				skin: 'n-skin',
				area: '560px', //宽高
			});
			dialogData.isSave = true;
			this.setState({
				dialogData: dialogData,
			})
		}
		// 快速编辑价格弹窗关闭
		onDialogClose() {
			var dialogData = this.state.dialogData;

			if (dialogData.main) {
				dialogData.main = layer.close(dialogData.main);
			}

			dialogData.isSave = false;

			this.setState({ dialogData });
		}
		onShowBigPic(imgSrc) {
			var html = '<div style="display:flex; align-items:center;justify-content:center; padding:16px; box-sizing:border-box;">';
			html += '<img src="' + imgSrc + '" style="width: 400px; height: 400px;" />';
			html += '</div>';
			layer.open({
				type: 1,
				title: false, // 不显示标题
				content: html,
				area: '434px', // 宽高
				btn: false,
				offset: '140px' // 只设置垂直坐标，水平保持居中
			});
		}
	    // 切割省份数据
		getProvinceData(list) {
			let provinces = [];
			provinces = list.map(function (item) {
				return item.split('/')[2]; // 分割字符串并获取第三个元素（省份名称）
			});
			return provinces;
		}
		// 获取运费模版规则tip
		getShippingFeeTemplate() {
			const that = this;
			const { details } = that.state;
			const id = details.SupplierFxUserId || 0;
			commonModule.Ajax({
				url: "/api/Station/GetShippingFeeTemplate?fxUserId=" + id,
				type: "GET",
				success: function (rsp) {
					if (rsp.Success) {
						let data = JSON.parse(rsp.Data);
						let list = [];
						if (data.OtherRules && data.OtherRules.length > 0) {
							list = data.OtherRules.map(function (item) {
								let addressData = JSON.parse(item.AddressData);
								let Provinces = [];
								if (addressData && addressData.Provinces && addressData.Provinces.length) {
									Provinces = that.getProvinceData(addressData.Provinces)
								}
								return {
									OtherType: item.OtherType,
									TemplateRule: item.TemplateRule,
									ProvincesText: Provinces.join()
								}
							});
						}
						// data.OtherRules = list;
						// 指定包邮地区
						data.IsFreeObj = list.find(function (item) {
							return item.OtherType === 1;
						}) || null;
						// 偏远地区
						data.IsRemoteObj = list.find(function (item) {
							return item.OtherType === 2;
						}) || null;
						that.setState({
							ShipFeeTemplateData: data
						});
					}
				}
			});
		}
		// 复制商品
		getCopyGoods(item, index) {
			const that = this;
			let formData = this.state.formData;
			let details = this.state.details;
			let copyList = [];
			if (item) {
				let obj = {
					FxUserId: item.SupplierFxUserId,
					SupplierProductUid: formData.uid
				}
				copyList.push(obj);
			}
			if (details.IsCopy) {
				layer.msg('商品已复制');
				return;
			}
			commonModule.Ajax({
				url: '/api/SupplierProduct/Copy',
				loadingMessage: "复制中",
				showMasker: false,
				contentType: 'application/json',
				data: JSON.stringify(copyList),
				success: function (rsp) {
					if (rsp.Success) {
						layer.msg('复制成功');
						details.IsCopy = true;
						that.setState({ details: details });
					} else {
						layer.msg(rsp.Message || rsp.Data || '失败');
					}
				}
			})
		}
		// 建立合作关系
		onCooperation(item, index) {
			let that = this;
			let formData = this.state.formData;
			let details = this.state.details;
			let obj = {
				FxUserId: item.SupplierFxUserId,
				SupplierProductUid: formData.uid
			}
			var dialogIndex = layer.open({
				type: 1,
				title: '提示消息', //不显示标题
				content: "铺货当前厂家货源，需与厂家联系并建立合作关系！",
				skin: 'n-skin',
				area: ['500px', 'auto'], //宽高
				btn: ['合作', '取消'],
				btn1: function () {
					commonModule.Ajax({
						url: '/api/SupplierProduct/Cooperation',
						loadingMessage: "加载中",
						showMasker: false,
						contentType: 'application/json',
						data: JSON.stringify(copyList),
						success: function (rsp) {
							if (rsp.Success) {
								layer.close(dialogIndex);
								layer.msg('已发送合作申请，请联系厂家同意合作！', {icon: 1}); 
								details.IsCooperation = true;
								that.setState({ details: details });
							} else {
								layer.msg(rsp.Message || rsp.Data || '失败');
							}
						}
					})
					return true;
				},
				btn2: function () {
					return true;
				}
			});
			
		}
		edmitProduct(item, index) {

			$('body').css({ overflowY: 'hidden' })
			var url = commonModule.rewriteUrlToMainDomain('/BaseProduct/BusinessCardCreateProduct') + '&CreateFrom=edmit&baseproductuid=' + item.SupplierProductUidStr;
			$("#businessCardCreateProductIframe").attr({ src: url });
			$("#businessCardCreateProductIframeWrap").addClass("active");
		}

		showTarDistribution(item){
			const {FromType} = this.state;
			//console.log("itemitem",item)
            var UidStr = item.SupplierProductUidStr; //'3000000500000006190'
            var FxUserId = item.SupplierFxUserId;
			let obj = {FxUserId };
			var edmitBaseproductuid = [];
			edmitBaseproductuid.push(UidStr);
            localStorage.setItem('edmitBaseproductuid',JSON.stringify(edmitBaseproductuid));
			if (FromType) {
				obj.FromType = FromType;
			}
			if (FromType == 3) {
				UidStr = '';
			}
            commonModule.tarDistribution('edmit', UidStr,obj);
		}

		// 动态计算dom显示更多
		onBoxSize(ref) {
			if (ref) {
				const domRect = ref.getBoundingClientRect();
				let domArr = ref.querySelectorAll('.list-item')
				let array = Array.from(domArr) //
				// 求出元素叠加后的总长度
				// 使用reduce方法计算总和
				let sum = array.reduce((acc, obj) => {
					// console.log(acc, obj)
					// 每个元素的宽度加上4（左右padding）
					return acc + obj.offsetWidth + 4;
				}, 0);
				if (sum > domRect.width) {
					ref.style.display = 'flex';
				}
			}
		}
		onToggleBox(id) {
			var $deliveryEl = $('#' + id);
			$deliveryEl.style.display = 'flex';
		}
		// 更多面单
		onExpressBillBox(list) {
			if (!list|| list.length == 0) return
			let arr = [];
			let arrExcess = [];
			list.map((item, index) => {
				let itemDom = <span className={"wu-pintaiIcon wu-small wu-mR4 " + item.ExpressBillType} title={item.ExpressBillName}></span>
				if (index < 6) {
					arr.push(itemDom)
				} else {
					arrExcess.push(itemDom)
				}
			})
			if (arrExcess.length) {
				let dom = <MyPopover
					placement="bottom"
					content={arrExcess}
					trigger='click'
					click={() => { }}

				>
					<span className="icon ExpressBill layui-content-li-icons-item iconfont icon-a-chevron-down1x" ></span>
				</MyPopover>
				arr.push(dom)
			}
			return arr
		}
		closeCreateProduct=function(){
			var iframe = document.getElementById('businessCardCreateProductIframe');
			var iframeWindow = iframe.contentWindow;
			iframeWindow.postMessage('closeCreateProduct', '*');
		}
		onBack(){
			//const {tag} = this.state;
			// if (tag == '3') {
			// 	window.history.back();
			// } else {
			// 	window.close()
			// }
			window.close()
		}
	    // 跳转到厂家小站
		goStation () {
			const {details,userType} = this.state;
			const id = details.SupplierFxUserId;
			commonModule.FxPermission(function (p) {
				commonModule.CheckPermission(function (success) {
					if (success) {
						thisFunc();
					}
					else return;
				}, p.SupplierCard);
			});

			var thisFunc = function(){

				if (id) {
					window.open(commonModule.rewriteUrl("/GeneralizeIndex/MyStationCard?id=" + id + "&userType=supplier"), '_self');
					return false;
				}
				// window.open(commonModule.rewriteUrl("/GeneralizeIndex/MyStationCard"), '_self');
			}

		}
		render() {
			const { skuList, theadData, details, dialogData, tag, ShipFeeTemplateData } = this.state;
			return (
				<div className="react-container">
					<div className="goodsInfo">
						<div class="goods-cont">
							<div className="header flex ">
								<i className="iconfont icon-xiayi hover" ></i>
								<span className="title" >商品详情</span>
							</div>
							<div className="cont flex">
								<div className="cont-l">
									<div className="preview-wrap">
										<div className="main-img">
											<a className="img" href="javascript:;">
											{
												details.ProductImages && details.ProductImages.length ?
												<img src={ details.ProductImages[0].ImageUrl} />
												: <div className="img noImg"><div className="imgbox" style={{backgroundImage: "url(/Content/images/distribution/icon-zhanweitu240820.png)"}} ></div></div>
											}
											</a>
											<div className="zoom-pup"></div>
											<div className="zoom-div">
												{
													details.ProductImages && details.ProductImages.length ?
													<img src={ details.ProductImages[0].ImageUrl} />
													: <div className="img noImg"><div className="imgbox" style={{backgroundImage: "url(/Content/images/distribution/icon-zhanweitu240820.png)"}} ></div></div>
												}
											</div>
										</div>
										<div className="spec-list">
											<a className="prev" id="img_prev" href="javascript:;" style={{ display: details.ProductImages && details.ProductImages.length > 4 ? 'block' : 'none' }}></a>
											<a className="next" id="img_next" href="javascript:;" style={{ display: details.ProductImages && details.ProductImages.length > 4 ? 'block' : 'none' }}></a>
											<div className="spec-items" style={{ width: details.ProductImages && details.ProductImages.length > 4 ? '250px' : '100%' }}>
												<ul style={{ left: '0' }} id="spec_img_ul">
													{
														details.ProductImages&& details.ProductImages.length ? details.ProductImages.map((item, index) => {

															return (
																<li className={index == 0 ? 'img-hover' : ''}>
																	<img src={item.ImageUrl || ''} />
																</li>
															)
														})
														: null

													}

												</ul>
											</div>
										</div>
									</div>
								</div>
								<div className="cont-r">
									<div className="title ellipsis" title={details.Subject}>{details.Subject}</div>
									<div className="txtbox flex">
										<div className="txt flex">
											<span className="label">上新时间</span>
											<span className="">{details.PublicTime || '-'}</span>
										</div>
									</div>

									<div class="price flex">
										<span className="label">采购价</span>
										<div className="pricebox">
											
											{
												details.MinPrice == details.MaxPrice ?
													!details.MinPrice && details.MinPrice != 0 ? <span>咨询报价</span>
													:<span className=""><span className="fs20">￥</span>{details.MinPrice || '0'}</span>
													:<span className=""><span className="fs20">￥</span>{details.MinPrice || '0'}-{details.MaxPrice || '0'}</span>
											}
										</div>
									</div>

									<div className="field">
										<div className="field-box flex">
											<span className="field-label">发货</span>
											<div className="field-list flex">
												<div className="field-item flex" style={{width: '100%'}}>
													<span className="label">发货仓地址</span>
													<div>广东省深圳市福田区福田街道深圳国际文化大厦2407店管家前台</div>
													
												</div>
											</div>
										</div>

									</div>


									{
										tag == 1 ?
											<div class="btn-box">
												 <div className="n-bButton" onClick={(e) => { this.edmitProduct(details) }}>编辑商品</div>
												<div className="n-bButton n-sActive" onClick={(e) => { this.showTarDistribution(details) }}>立即铺货</div>
											</div>
											:
											<div class="btn-box">
												<div className="n-bButton" onClick={(e) => { this.showTarDistribution(details) }}>立即铺货</div>
												{
													// !details.IsCopy ?
													// <div className="n-bButton n-sActive" onClick={(e) => { e.stopPropagation(); this.getCopyGoods(details) }}>复制到基础商品</div>
													// : <div className="n-bButton n-pActive stop" style={{ color: "rgba(0, 0, 0, 0.3)", opacity: "1" }}>已复制</div>
												}
												{
													details.IsCooperation ?
													<div className="n-bButton n-sActive" onClick={(e) => { e.stopPropagation(); this.onCooperation(details) }}>合作</div>
													:<div className="n-bButton n-pActive stop" style={{ color: "rgba(0, 0, 0, 0.3)", opacity: "1" }}>已合作</div>
												}
											</div>
									}
								</div>
							</div>

						</div>
					</div>

					<div className="details">
						<div className="cont flex">
							<div className="cont-l" style={{display: "none"}}>>
								<div className="suppliers-info flex">
									<img src={details.AvatarUrl || '/Content/images/avatarUrl.png'} alt="" />
									<div className="suppliers-info-cont">
										<div className="suppliers-info-cont-t flex">
											<span className="suppliers-info-cont-t-name">{details.SupplierRemark}</span>
											{
												details.CompanyInfoPerfectState ?
													<div>
														<span className="suppliers-info-cont-t-prove flex n-dColor hover" onClick={() => this.onToggle('CompanyInfo')}>
															<i class="iconfont icon-a-secured1x"></i>
															<span class="form-info-m-l-6">已完善企业证明</span>
															<i class="iconfont icon-a-chevron-down1x"></i>
														</span>
														<div className="n-tooltip n-leftUp form-common-tip-wrap form-common-tip-w-340" id="CompanyInfo" style={{ display: "none" }}>
															<div className="companyInfo-list flex">
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">营业执照</span>
																	<div className="companyInfo-list-item-value hover" style={{color:"#0888FF"}} onClick={() => this.onShowBigPic(details.CompanyInfo[0].LicensePic)}>查看图片</div>
																</div>
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">公司名称</span>
																	<div className="companyInfo-list-item-value">{details.CompanyInfo[0].CompanyName}</div>
																</div>
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">统一社会信用代码</span>
																	<div className="companyInfo-list-item-value">{details.CompanyInfo[0].USCC}</div>
																</div>
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">经营期限</span>
																	<div className="companyInfo-list-item-value">{details.CompanyInfo[0].BusinessTerm}</div>
																</div>
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">经营地址</span>
																	<div className="companyInfo-list-item-value">
																	{details.CompanyInfo[0].Province}{details.CompanyInfo[0].City}{details.CompanyInfo[0].County}{details.CompanyInfo[0].Address}
																	</div>
																</div>
																<div className="companyInfo-list-item flex">
																	<span className="companyInfo-list-item-label">主营类型</span>
																	<div className="companyInfo-list-item-value">{details.CompanyInfo[0].SubjectType}</div>
																</div>
															</div>
														</div>
													</div>
													:
													<span className="suppliers-info-cont-t-prove flex not-present">
														<i class="iconfont icon-a-secured1x"></i>
														<span class="form-info-m-l-6">未完善企业证明</span>
														<i class="iconfont icon-a-chevron-down1x"></i>
													</span>
											}

										</div>
									</div>
								</div>
								<div className="n-bButton n-sActive mB8" onClick={() => this.onBusinessCard()}><i className="iconfont icon-a-call1x mR8"></i>业务联系人</div>
								<div className="n-bButton n-sActive" onClick={() => this.goStation()}><i className="iconfont icon-a-home1x mR8"></i>厂家小站</div>
							</div>
							<div className="cont-l"></div>
							<div className="cont-r">
								<div className="title" style={{display: theadData && theadData.length > 0 ? 'block' : 'none'}}>商品规格</div>

								<div className="layui-mytable" style={{display: theadData && theadData.length > 0 ? 'block' : 'none'}}>
									<table className="stockup_table_content new-mytable">
										<thead>
											<tr >
												{
													theadData.map((item, index) => {
														return (
															<th key={index + "theadData"} style={{ minWidth: item.minWidth + 'px' }}>
																<div style={{ textAlign: item.textAlign }}>
																	{item.value}
																</div>
															</th>
														)
													})
												}

											</tr>
										</thead>
										<tbody>
											{this.onFormatTbody()}
										</tbody>
									</table>
								</div>
								<div className="title" style={{display: details.CategoryAttribute && details.CategoryAttribute.length > 0 ? 'block' : 'none'}}>商品属性</div>
								<div class="attribute flex">
									{
										details.CategoryAttribute && details.CategoryAttribute.map((item, index) => {
											return (
												<div class="attribute-item flex">
													<span className="label">{item.Name}</span>
													<span>{ ( item.FieldType=="checkbox" || item.FieldType=="checkboxlist") && item.Value.length ? item.valueStr : item.Value}</span>
												</div>
											)
										})
									}

								</div>
								<div className="title" style={{display: details.DescriptionStr && details.DescriptionStr.length > 0 ? 'block' : 'none'}}>商品描述</div>
								<div className="descriptionStr" >
									{
										details.DescriptionStr && details.DescriptionStr.map((item, index) => {
											return (
												<div className="descriptionStr-item" key={index + "descriptionStr"}>
													<img src={item} />
												</div>
											)
										})
									}
								</div>

							</div>
						</div>

					</div>

					<div id="dialogBusinessCard" style={{ display: 'none' }}>
						<div className="dialog-wrap">
							<div className="dialog-wrap-cont">
								{
									details.Contacts ?
										<ul className="business-contact-ul">
											{
												details.Contacts.map((item, index) => {
													return (
														<li className="contact-card-li" >
															<div className="contact-card-name">{item.JobTitle} {item.Name}</div>
															<div className="contact-card-data form-info-m-t-8">
																<div className="input-common-col">
																	<i className="iconfont icon-a-call1x"></i>
																	<span className="form-info-m-l-6">{item.Tel}</span>
																</div>
																<div className="input-common-col scan-code-wrap" onClick={() => this.onShowBigPic(item.ImageUrl)}>
																	<i className="iconfont icon-a-qrcode1x"></i>
																	<span className="form-info-m-l-6">扫码加好友</span>
																</div>
															</div>
														</li>
													)
												})
											}
										</ul>
										: <div className="business-contact-info">
											<div><i className="iconfont icon-a-clear1x dailog-common-icon"></i></div>
											<div className="dailog-common-title form-info-m-t-8">未提供业务联系人</div>
										</div>
								}
							</div>
						</div>
						<div className="dialogBtns flex">
							<div><span></span></div>
							<div className="btnBox flex">
								<div className="n-mButton " onClick={() => this.onDialogClose()}>关闭</div>
							</div>
						</div>
					</div>

					<div class="new-full-mask" id="businessCardCreateProductIframeWrap">
						<div class="full-mask-back" onClick={() => this.closeCreateProduct()}></div>
						<div class="full-mask-content-wrapper full-mask-right" style={{ width: '1088px', height: '100vh' }}>
							<iframe id="businessCardCreateProductIframe" style={{ width: '100%', height: '100%' }} frameborder="0"></iframe>
						</div>
					</div>

				</div>
			)
		}
	}
	ReactDOM.render(<DistributionDetails />, document.getElementById("distributionDetails"));
</script>
