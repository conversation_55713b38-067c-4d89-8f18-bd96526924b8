using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.InstantRetail;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct
{
    public class WarehouseHander : ShopFieldMappingHander
    {
        public List<ShopFieldMappingDetails> BuildParms(List<int> shopIds)
        {
            var platformShops = new ShopService("listing").GetShopsAndShopExtensionFunc(shopIds, isCheckAuth: true, isProcessPddShopExtension: false);
            if (platformShops == null || !platformShops.Any()) throw new LogicException("当前店铺不存在铺货的应用，请先订购铺货应用或添加相关的店铺");

            var result = new List<ShopFieldMappingDetails>();
            foreach (var platformShop in platformShops)
            {
                try
                {
                    var ptWarehouseData = new List<InstantRetailWarehousesALLDto>();
                    if (platformShop.PlatformType == PlatformType.TouTiaoSaleShopSupply.ToString())
                    {
                        var service = new ZhiDianNewPlatformService(platformShop);
                        ptWarehouseData = service.GetWarehouseApi();
                    }

                    if (ptWarehouseData != null && ptWarehouseData.Count > 0)
                    {
                        var shopFieldMappingDetails = new ShopFieldMappingDetails
                        {
                            ShopId = platformShop.Id,
                            ShopName = platformShop.ShopName
                        };
                        foreach (var item in ptWarehouseData)
                            shopFieldMappingDetails.AddMappingDetailsList(item.out_warehouse_id, item.name);

                        result.Add(shopFieldMappingDetails);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"获取店铺:{platformShop.ShopName}的发货仓异常，信息：{ex.Message}. {ex.StackTrace}", "WarehouseHander.txt");
                }
            }
            return result;
        }

        public List<InstantRetailWarehousesALLDto> GetPtWarehouseData(string platformType, Shop platformShop)
        {
            var ptWarehouseData = new List<InstantRetailWarehousesALLDto>();
            if (platformType == PlatformType.TouTiaoSaleShopSupply.ToString())
            {
                ptWarehouseData = new ZhiDianNewPlatformService(platformShop).GetWarehouseApi();
            }

            return ptWarehouseData;
        }
    }
}
