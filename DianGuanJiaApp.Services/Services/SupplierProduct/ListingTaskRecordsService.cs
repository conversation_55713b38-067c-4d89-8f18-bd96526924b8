using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Entity.Collect;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Entity.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Model.CrossBorder;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Model.SupplierProduct;
using DianGuanJiaApp.Data.Model.Tools;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Data.Repository.SupplierProduct.ListingProduct;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.CrossBorder;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using RabbitMQ.Client.Framing.Impl;
using DianGuanJiaApp.ViewModels.Models;
using NPOI.HSSF.Record;
using vipapis.marketplace.product;
using vipapis.pg;
using WebSocketSharp;
using static DianGuanJiaApp.Data.Repository.FinancialSettlementRepository;
using Newtonsoft.Json;
using Nest;
using System.Collections;

namespace DianGuanJiaApp.Services.Services.SupplierProduct
{
    /// <summary>
    /// 铺货任务服务层
    /// </summary>
    public class ListingTaskRecordsService : SupplierProductBaseService<ListingTaskRecords>
    {
        private BaseProductEntityService _baseProductService;
        private BaseProductSkuService _baseProductSkuService;
        private BaseProductSkuSupplierConfigService _baseProductSkuSupplierConfigService;
        private PtProductInfoService _ptProductInfoService;
        private ListingTaskBusinessAbnormalService _businessAbnormalService;
        private readonly ListingTaskRecordsRepository _repository;
        private readonly SharePathFlowRepository _sharePathFlowRepository;
        private readonly SharePathFlowNodeRepository _sharePathFlowNodeRepository;
        private readonly UserListingSettingService _userListingSettingService;
        private readonly UserSupplierStatusService _userSupplierStatusService;
        private readonly UserFxService _userFxService;
        private SupplierUserService _supplierUserService;
        private PlatformCategorySupplierRepository platformCategorySupplierRepository;
        public const int BATCH_SIZE = 500;//每批处理数量

        /// <summary>
        /// 获取默认的数据库连接
        /// </summary>
        public ListingTaskRecordsService()
        {
            _repository = new ListingTaskRecordsRepository();

            var curFxUserId = BaseSiteContext.CurrentNoThrow.CurrentFxUserId;
            var dbConfigModel = new ProductDbConfigRepository().GetDbConfigModel(curFxUserId);
            var connectionString = dbConfigModel?.ConnectionString;
            var isUseMySql = dbConfigModel?.IsMySQL ?? true;

            _baseProductService = new BaseProductEntityService(connectionString, isUseMySql);
            _baseProductSkuService = new BaseProductSkuService(connectionString, isUseMySql);
            _baseProductSkuSupplierConfigService = new BaseProductSkuSupplierConfigService(connectionString, isUseMySql);
            _ptProductInfoService = new PtProductInfoService(connectionString, isUseMySql);
            _businessAbnormalService = new ListingTaskBusinessAbnormalService();
            _sharePathFlowRepository = new SharePathFlowRepository();
            _sharePathFlowNodeRepository = new SharePathFlowNodeRepository();
            _userListingSettingService = new UserListingSettingService();
            _userSupplierStatusService = new UserSupplierStatusService();
            _userFxService = new UserFxService();
            _supplierUserService = new SupplierUserService();
            platformCategorySupplierRepository = new PlatformCategorySupplierRepository();
        }

        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public ListingTaskRecordsService(string connectionString, bool isUseMySql)
        {
            _repository = new ListingTaskRecordsRepository(connectionString, isUseMySql);

            _baseProductService = new BaseProductEntityService(connectionString, isUseMySql);
            _baseProductSkuService = new BaseProductSkuService(connectionString, isUseMySql);
            _baseProductSkuSupplierConfigService = new BaseProductSkuSupplierConfigService(connectionString, isUseMySql);
            _ptProductInfoService = new PtProductInfoService(connectionString, isUseMySql);

            _businessAbnormalService = new ListingTaskBusinessAbnormalService();
            _sharePathFlowRepository = new SharePathFlowRepository();
            _sharePathFlowNodeRepository = new SharePathFlowNodeRepository();
            _userListingSettingService = new UserListingSettingService();
            _userSupplierStatusService = new UserSupplierStatusService();
            _userFxService = new UserFxService();
            _supplierUserService = new SupplierUserService();
        }


        /// <summary>
        /// 添加（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Add(ListingTaskRecords model)
        {
            if (model == null)
                return;
            if (string.IsNullOrEmpty(model.ListingTaskCode))
            {
                model.ListingTaskCode = Guid.NewGuid().ToString().ToShortMd5();
                if (model.Ext != null)
                    model.Ext.ListingTaskCode = model.ListingTaskCode;
            }

            if (model.CreateTime < Convert.ToDateTime("2000-01-01"))
                model.CreateTime = DateTime.Now;
            if (model.UpdateTime < Convert.ToDateTime("2000-01-01"))
                model.UpdateTime = DateTime.Now;
            if (model.Ext != null)
            {
                if (model.Ext.CreateTime < Convert.ToDateTime("2000-01-01"))
                    model.Ext.CreateTime = DateTime.Now;
                if (model.Ext.UpdateTime < Convert.ToDateTime("2000-01-01"))
                    model.Ext.UpdateTime = DateTime.Now;
            }

            _repository.Add(model);
        }

        /// <summary>
        /// 批量添加（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<ListingTaskRecords> models)
        {
            if (models == null || models.Any() == false)
                return;
            models.ForEach(model =>
            {
                if (string.IsNullOrEmpty(model.ListingTaskCode))
                {
                    model.ListingTaskCode = Guid.NewGuid().ToString().ToShortMd5();
                    if (model.Ext != null)
                        model.Ext.ListingTaskCode = model.ListingTaskCode;
                }

                if (model.CreateTime < Convert.ToDateTime("2000-01-01"))
                    model.CreateTime = DateTime.Now;
                if (model.UpdateTime < Convert.ToDateTime("2000-01-01"))
                    model.UpdateTime = DateTime.Now;
                if (model.Ext != null)
                {
                    if (model.Ext.CreateTime < Convert.ToDateTime("2000-01-01"))
                        model.Ext.CreateTime = DateTime.Now;
                    if (model.Ext.UpdateTime < Convert.ToDateTime("2000-01-01"))
                        model.Ext.UpdateTime = DateTime.Now;
                }
            });
            _repository.BatchAdd(models);
        }

        /// <summary>
        /// 获取单个
        /// </summary>
        /// <param name="code"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ListingTaskRecords GetByCode(string code, int fxUserId = 0)
        {
            return _repository.GetByCode(code, fxUserId);
        }

        /// <summary>
        /// 更新以下字段：BusinessStatus、ProcessBusinessTime、NextProcessBusinessTime、ErrorMessage
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateBusinessStatus(ListingTaskRecords model)
        {
            return _repository.UpdateBusinessStatus(model);
        }

        /// <summary>
        /// 更新以下字段：BusinessResult
        /// </summary>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public int UpdateExtBusinessProcessResult(string code, string message)
        {
            return _repository.UpdateExtBusinessProcessResult(code, message);
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<ListingTaskRecords> GetList(List<string> codes, string selectFields = "*",
            string whereFieldName = "ListingTaskCode")
        {
            if (codes == null || codes.Any() == false)
                return new List<ListingTaskRecords>();

            var list = new List<ListingTaskRecords>();
            var batchSize = BATCH_SIZE;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetList(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<ListingTaskRecords>> GetPageList(ListingTaskRecordsQuery query)
        {
            return _repository.GetPageList(query);
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<ListingTaskRecords>> GetRecordPageList(ListingTaskRecordQuery query)
        {
            // 任务状态转换
            if (!string.IsNullOrEmpty(query.TaskStatus))
                query.Status = ListingTaskRecordQuery.StatusDic.TryGetValue(query.TaskStatus, out var queryStatus)
                    ? queryStatus
                    : null;
            var tuple = _repository.GetRecordPageList(query);

            List<ListingTaskRecords> model = tuple.Item2;
            if (tuple != null && tuple.Item1 != 0)
            {

                //补一下跨境的具体站点店铺,目前查询的结果包含其他平台店铺和Tk的全球店铺
                var parentShopIds = tuple.Item2.Where(a => a.TargetPlatformType.Equals("TikTok")).Select(a => a.TargetShopId).Distinct().ToList();
                var allTkShopModel = GetAllTkShop(parentShopIds, query);
                if (allTkShopModel.IsNotNullAndAny())
                {
                    model.AddRange(allTkShopModel);
                }
                tuple = new Tuple<int, List<ListingTaskRecords>>(tuple.Item1 + allTkShopModel.Count(), model);
            }
            var curFxUserId = SiteContext.Current.CurrentFxUserId;

            // 查询相关异常数据
            var codes = tuple.Item2.Select(a => a.ListingTaskCode).ToList();
            var abnormalList = _businessAbnormalService.GetList(codes).Where(x => x.IsDeleted == false).ToList();

            // 拿到店铺Id查店铺名称信息
            var shopIds = tuple.Item2.Where(a => !a.TargetPlatformType.Equals("TikTok")).Select(a => a.TargetShopId).Distinct().ToList();
            var userIdByShopId = new FxUserShopService().GetUserIdByShopId(shopIds, "ShopId,NickName");
            // 跨境的根据店铺Id获取全球店铺信息
            var foriegnShopIds = tuple.Item2.Where(a => a.TargetPlatformType.Equals("TikTok")).Select(a => a.TargetShopId).Distinct().ToList();
            var globalUserIdByShopId = new ShopService().GetAllTkUserIdByShopId(foriegnShopIds, "Id,NickName");


            // 获取厂家信息
            var suppliers = _supplierUserService.GetSupplierList(curFxUserId, needEncryptAccount: true);
            var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId)
                .ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

            var platformcateSvc = new PlatformCategorySupplierService();
            var CollectClaimRelationService = new CollectClaimRelationService();
            var fxUserForeignShopService = new FxUserForeignShopService();
            // 组装数据
            tuple.Item2.ForEach(a =>
            {
                var abnormal = abnormalList.FindAll(b => b.ListingTaskCode == a.ListingTaskCode);
                var shopInfo = userIdByShopId.FirstOrDefault(b => b.ShopId == a.TargetShopId);
                //加个跨境的判断
                if (shopInfo != null)
                {
                    a.TargetShopName = shopInfo?.NickName;
                }
                else
                {
                    var foriegnShopInfo = globalUserIdByShopId?.FirstOrDefault(b => b.Id == a.TargetShopId);
                    if (foriegnShopInfo != null)
                    {
                        var foriegnShop = fxUserForeignShopService.GetByShopId(new FxUserForeignShop
                        {
                            FxUserId = query.FxUserId,
                            ShopId = foriegnShopInfo.Id,
                            PlatformType = "TikTok"
                        });
                        a.TargetShopName = foriegnShopInfo.NickName;
                        a.SubPlatformType = foriegnShop?.SubPlatformType;
                    }
                }

                a.TaskStatus = ListingTaskRecordModel.StatusDic.TryGetValue(a.Status, out var status) ? status : "";
                a.ImageUrl = ImgHelper.ChangeImgUrl(a.ImageUrl);

                // 获取异常信息
                if (abnormal.Any())
                {
                    if (a.AbnormalList == null) a.AbnormalList = new List<AbnormalModel>();
                    abnormal.ForEach(x =>
                    {
                        a.AbnormalList.Add(new AbnormalModel
                        {
                            ErrorMessage = x.ErrorMessage,
                            BusinessType = x.BusinessType
                        });
                    });
                }

                // 非自营，设置厂家信息
                if (a.FxUserId != curFxUserId)
                {
                    if (supplierList == null || !supplierList.Any()) return;
                    var supplierRes = supplierList.FirstOrDefault(t => t.Key == a.FxUserId);
                    a.SupplierName = supplierRes.Value;
                }

                //跨境的看一下货源，显示采集箱货源
                if (a.TargetPlatformType.Equals("TikTok") && a.BaseProductUId != 0)
                {
                    ClaimShopProduct claimShopProduct = CollectClaimRelationService.GetClaimShopProductByUid(a.BaseProductUId.ToString(), a.TargetShopId, query.FxUserId);
                    if (claimShopProduct != null)
                    {
                        a.SupplierName = claimShopProduct.Source;
                    }
                }

                try
                {
                    if (shopInfo != null)
                    {
                        // 获取商品状态
                    ListingProductStatusEnum? productStatus = null;
                    if (a.TargetPlatformType == PlatformType.TouTiao.ToString())
                    {
                        productStatus = platformcateSvc.GetProductAuditList(shopInfo.ShopId, a.ProductId, a.ListingProductStatus);
                    }
                    else if (a.TargetPlatformType == PlatformType.Pinduoduo.ToString())
                    {
                        productStatus = platformcateSvc.GetProductAuditListByPdd(shopInfo.ShopId, a.ProductId, a.ListingProductStatus);
                    }
                    else if (a.TargetPlatformType == PlatformType.KuaiShou.ToString()) 
                    {
                        productStatus = platformcateSvc.GetProductAuditListByKs(shopInfo.ShopId, a.ProductId, a.ListingProductStatus);
                    }
                        if (productStatus != null)
                        {
                            a.ListingProductStatus = (int)productStatus;
                            int issuccess = _repository.UpdateListingProductStatus(a); // 更新铺货记录表，商品状态
                            if (issuccess <= 0)
                            {
                                Log.WriteError($"铺货日志列表查询, 修改状态失败 issuccess={issuccess} ListingProductStatus={a.ListingProductStatus} ListingTaskCode={a.ListingTaskCode}", "GetProductAuditList.txt");
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    Log.WriteError($"铺货日志列表查询报错：{ex.Message} 堆栈：【{ex.StackTrace}】 扩展数据为空", "GetProductAuditList.txt");
                }
            });
            return tuple;

        }

        /// <summary>
        /// 获取跨境站点下的所有站点店铺，目前只有Tk平台
        /// </summary>
        /// <param name="parentShopIds"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public List<ListingTaskRecords> GetAllTkShop(List<int> parentShopIds, ListingTaskRecordQuery query)
        {
            ShopService shopService = new ShopService();
            List<Shop> resultShops = new List<Shop>();
            List<int> targetShopIds = new List<int>();
            List<ListingTaskRecords> result = new List<ListingTaskRecords>();
            foreach (var id in parentShopIds)
            {
                Shop parentShop = shopService.GetShopById(id);
                List<Shop> shops = shopService.GetListByParentShopId(parentShop.ShopId, "TikTok");
                if (shops.IsNotNullAndAny())
                {
                    resultShops.AddRange(shops);
                }
            }
            if (resultShops.IsNullOrEmpty())
            {
                return result;
            }
            targetShopIds = resultShops.Select(s => s.Id).ToList();
            foreach (var targetShopId in targetShopIds)
            {
                query.TargetShopId = targetShopId;
                var tuple = _repository.GetRecordPageList(query);
                if (tuple.Item1 > 0)
                {
                    result.AddRange(tuple.Item2);
                }
            }
            return result;
        }

        public Tuple<bool, string, GlobalProductSearchDetailRes> GetTkListingTaskRecordDetail(PhGlobalProductDetailModel model)
        {
            ListingTaskRecords listingTaskRecords = _repository.GetTkRecordTotalInfoByCode(model.ListingTaskCode, model.FxUserId);
            ListingTaskRecordsExt listingTaskRecordsExt = listingTaskRecords.Ext;
            if (listingTaskRecords == null || listingTaskRecordsExt == null)
            {
                Log.WriteError($"铺货日志获取Tk的铺货日志信息失败，数据为空", "GetProductAuditList.txt");
                return new Tuple<bool, string, GlobalProductSearchDetailRes>(false, "铺货日志获取Tk的铺货日志信息失败，数据为空", null);
            }
            if (!listingTaskRecords.TargetPlatformType.Equals("TikTok"))
            {
                return new Tuple<bool, string, GlobalProductSearchDetailRes>(false, "铺货日志获取Tk的铺货日志信息失败，此条数据非Tk的铺货日志数据", null);
            }
            GlobalProductService globalProductService = new GlobalProductService();
            GlobalProductRepository globalProductRepository = new GlobalProductRepository();
            CollectClaimRelationService collectClaimRelationService = new CollectClaimRelationService();
            var categories = new PlatformCategorySupplierRepository().GetShopCatesByPlatform(PlatformType.TikTok.ToString());
            var dbCateDic = new Dictionary<string, PlatformCategory>();
            if (categories.Any())
            {
                dbCateDic = categories.ToDictionary(x => x.CateId, y => y);
            }
            if (!string.IsNullOrEmpty(listingTaskRecords.PackJson))
            {
                //处理数据
                var gp = listingTaskRecords.PackJson.ToObject<GlobalProduct>();
                var ext = gp.globalProductExt;
                List<GlobalProductSku> skuList = gp.globalProductSkuList;
                Shop parentShop = null;
                try
                {
                    parentShop = new ShopRepository().Get(gp.ShopId);
                    //把所有图片uri改成url传给前端
                    GetGlobalProductImageUrlInfo(gp, ext, skuList);

                    //skuList转换一下，去json,并获取sku的属性
                    List<GlobalProductSkuSaleAttributeRes> allSkuAttributeList = new List<GlobalProductSkuSaleAttributeRes>();
                    List<SkuGetProductAttribute> SkuAttributeTypes = new List<SkuGetProductAttribute>();
                    List<GlobalProductSkuDeleteJsonModel> noJsonSkuList;
                    globalProductService.GetSkuAttributeListInfo(gp, skuList, allSkuAttributeList, out SkuAttributeTypes, out noJsonSkuList);

                    //查店铺发布情况并返回
                    List<GlobalProductRegionsRes> globalProductRegionsRes = new List<GlobalProductRegionsRes>();
                    globalProductService.GetPublishInfo(model.FxUserId, gp, ext, globalProductRegionsRes);

                    //查资质,这部分若无数据走tk接口
                    string CertificationId = null;
                    var Certification = ext.Certification.ToObject<List<GlobalProductDetailCertification>>();
                    CertificationId = globalProductService.GetCertification(gp, CertificationId, Certification);

                    //类目属性
                    string CategoryAttribute = collectClaimRelationService.GetCategoryMetadata(gp.CategoryId, gp.ShopId, PlatformType.TikTok.ToString(), model.FxUserId, null).ToJson();
                    List<CategoryInfo> CategoryInfoList = string.IsNullOrEmpty(gp.CategoryId) ? null : collectClaimRelationService.GetCategoryPath(dbCateDic, gp.CategoryId);

                    //溯源返回视频url
                    string SourceVideoUrl = null;
                    SourceVideoUrl = globalProductService.GetSourceVideoUrl(model.FxUserId, collectClaimRelationService, gp, SourceVideoUrl);

                    GlobalProductSearchDetailRes info = new GlobalProductSearchDetailRes
                    {
                        PlatformId = gp.PlatformId,
                        UniqueCode = listingTaskRecords.ListingTaskCode,
                        Title = gp.Title,
                        ShopId = gp.ShopId,
                        ShopName = parentShop.ShopName,
                        PlatformType = gp.PlatformType,
                        ProductSource = gp.ProductSource,
                        ProductSourceUrl = gp.ProductSourceUrl,
                        ProductSourceUid = gp.ProductSourceUid,
                        FxUserId = gp.FxUserId,
                        CreateFrom = gp.CreateFrom,
                        Description = gp.Description,
                        PlatformCreatetime = gp.PlatformCreatetime.ToString("yyyy-MM-dd HH:mm:ss"),
                        PlatformUpdatetime = gp.PlatformUpdatetime.ToString("yyyy-MM-dd HH:mm:ss"),
                        BrandID = gp.BrandID,
                        CategoryId = gp.CategoryId,
                        GlobalSellerId = gp.GlobalSellerId,
                        ImageUrl = gp.ImageUrl,
                        PublishedSite = gp.PublishedSite,
                        CreateTime = gp.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        UpdateTime = gp.UpdateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        ProductStatus = gp.ProductStatus,
                        Video = ext.Video,
                        PackageDimensions = ext.PackageDimensions.ToObject<PackageDimensionReq>(),
                        PackageWeight = ext.PackageWeight.ToObject<PackageWeightReq>(),
                        ImageJson = ext.ImageJson,
                        Certification = ext.Certification.ToObject<List<GlobalProductDetailCertification>>(),
                        CertificationId = CertificationId,
                        SizeChart = ext.SizeChart.ToObject<ProductDetailSizeChart>(),
                        Manufacturer = ext.Manufacturer.ToObject<GlobalCreateProductManufactoryReq>(),
                        Region = ext.Region.ToObject<List<GlobalLocalProducts>>(),
                        CategoryJson = ext.CategoryJson,
                        AttributeTypes = ext.AttributeTypesJson.ToObject<List<GetProductAttribute>>(),
                        globalProductSkus = noJsonSkuList,
                        SkuAttributeTypes = SkuAttributeTypes,
                        publishedShopList = globalProductRegionsRes,
                        CategoryAttribute = CategoryAttribute,
                        CategoryInfoList = CategoryInfoList,
                        SourceVideoUrl = SourceVideoUrl
                    };

                    return new Tuple<bool, string, GlobalProductSearchDetailRes>(true, string.Empty, info);
                }
                catch (Exception ex)
                {
                    return new Tuple<bool, string, GlobalProductSearchDetailRes>(false, "获取Tk的铺货日志详情失败", null);
                }
            }
            else
            {
                return new Tuple<bool, string, GlobalProductSearchDetailRes>(false, "铺货日志获取Tk的铺货日志的数据包为空", null);
            }

        }

        /// <summary>
        /// Tk铺货任务重新铺货详情接口在这里把所有图片uri换成url
        /// </summary>
        /// <param name="globalProduct"></param>
        /// <param name="globalProductExt"></param>
        /// <param name="globalProductSkuList"></param>
        private void GetGlobalProductImageUrlInfo(GlobalProduct globalProduct, GlobalProductExt globalProductExt, List<GlobalProductSku> globalProductSkuList)
        {
            if (!string.IsNullOrEmpty(globalProduct.ImageUrl))
            {
                globalProduct.ImageUrl = string.Join(",", globalProduct.ImageUrl.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(uri => TkPhGetImageUrl(uri)));
            }
            else
            {
                globalProduct.ImageUrl = null;
            }
            if (!string.IsNullOrEmpty(globalProductExt.ImageJson))
            {
                var extimageUris = globalProductExt.ImageJson.ToObject<List<ImageUri>>();
                extimageUris = extimageUris.Select(i =>
                {
                    i.uri = TkPhGetImageUrl(i.uri);
                    return i;
                }).ToList();
                globalProductExt.ImageJson = extimageUris.ToJson();
            }
            if (!string.IsNullOrEmpty(globalProductExt.Certification))
            {
                var certifications = globalProductExt.Certification.ToObject<List<GlobalProductDetailCertification>>();
                if (certifications.IsNotNullAndAny())
                {
                    List<GlobalProductDetailCertification> newCertifications = new List<GlobalProductDetailCertification>();
                    foreach (GlobalProductDetailCertification ct in certifications)
                    {
                        GlobalProductDetailCertification newCt = ct;
                        List<GetGlobalProductCertificationsImages> images = ct.images;
                        if (images.IsNotNullAndAny())
                        {
                            List<GetGlobalProductCertificationsImages> newImages = new List<GetGlobalProductCertificationsImages>();
                            foreach (GetGlobalProductCertificationsImages cimg in images)
                            {
                                GetGlobalProductCertificationsImages newCimg = cimg;
                                newCimg.uri = TkPhGetImageUrl(newCimg.uri);
                                newImages.Add(newCimg);
                            }
                            newCt.images = newImages;
                            newCertifications.Add(newCt);
                        }

                    }
                    globalProductExt.Certification = newCertifications.ToJson();
                }
            }
            if (!string.IsNullOrEmpty(globalProductExt.SizeChart))
            {
                var sizeChart = globalProductExt.SizeChart.ToObject<ProductDetailSizeChart>();
                if (sizeChart.image != null)
                {
                    sizeChart.image.uri = TkPhGetImageUrl(sizeChart.image.uri);
                    globalProductExt.SizeChart = sizeChart.ToJson();
                }
            }
            if (globalProductSkuList.IsNotNullAndAny())
            {
                globalProductSkuList = globalProductSkuList.Select(g =>
                {
                    if (!string.IsNullOrEmpty(g.ImagesUrl))
                    {
                        g.ImagesUrl = string.Join(",", g.ImagesUrl.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(uri => TkPhGetImageUrl(uri)));
                    }
                    g.Identifiercode = null;
                    return g;

                }).ToList();
            }
        }

        /// <summary>
        /// 获取铺货任务店铺信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<ListingShopModel> GetShopList(int fxUserId, string platformType)
        {
            //非跨境部分逻辑， 获取任务信息
            var shopIds = _repository.GetExistShopIds(fxUserId);
            var foreignShopIds = _repository.GetForeignExistShopIds(fxUserId);
            var shopModelList = new List<ListingShopModel>();

            if (string.IsNullOrWhiteSpace(platformType) || platformType == "TikTok")
            {
                // 跨境的根据店铺Id获取全球店铺信息
                var globalUserIdByShopId = new ShopService().GetTkUserIdByShopId(foreignShopIds, "*");

                List<ListingShopModel> globalShopModelList = new List<ListingShopModel>();
                if (globalUserIdByShopId.IsNotNullAndAny())
                {
                    globalShopModelList = globalUserIdByShopId.Select(a => new ListingShopModel
                    {
                        ShopId = a.Id,
                        ShopName = a.NickName
                    }).ToList();
                    shopModelList.AddRange(globalShopModelList);
                }
            }

            if (string.IsNullOrWhiteSpace(platformType) || platformType != "TikTok")
            {
                // 非跨境的根据店铺Id获取店铺信息
                var userIdByShopId = new FxUserShopService().GetUserIdByShopId(shopIds, "ShopId,NickName", platformType);
                shopModelList.AddRange(userIdByShopId.Select(a => new ListingShopModel
                {
                    ShopId = a.ShopId,
                    ShopName = a.NickName
                }).ToList());
            }

            return shopModelList;
        }

        /// <summary>
        /// Tk跨境重新铺货功能跟其他业务不同，单独走这个方法
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ReturnedModel<ListingTaskStatusStat> TkUpdateListingProductInfo(GlobalProductUpdateModel originModel, int fxUserId)
        {
            GlobalProductService globalProductService = new GlobalProductService();
            //数据转换
            GlobalProductEditModel model = globalProductService.TransformEditGlobalProductModel(originModel);
            ListingTaskRecords task = _repository.GetByCode(model.UniqueCode, fxUserId);
            ListingTaskRecordsExt taskExt = task.Ext;
            //这里这就是个存数据的类，全球商品、店铺商品都用的这个类,下面需要处理数据，转换成GlobalProduct、GlobalProductExt、GlobalProductSku在数据库内的存储格式，json也要针对性解析
            GlobalProduct globalProduct = taskExt.PackJson.ToObject<GlobalProduct>();
            globalProduct.PlatformId = model.global_product_id;
            globalProduct.Title = model.title;
            globalProduct.Description = model.description;
            globalProduct.CategoryId = model.category_id;
            globalProduct.BrandID = model.brand_id;
            if (model.main_images != null && model.main_images.Any(image => image != null && !string.IsNullOrEmpty(image.uri)))
            {
                globalProduct.ImageUrl = string.Join(",", model.main_images
                    .Where(image => image != null && !string.IsNullOrEmpty(image.uri))
                    .Select(image => globalProductService.GetImageTkUri(image.uri)));
            }
            else
            {
                globalProduct.ImageUrl = null;
            }
            GlobalProductExt globalProductExt = globalProduct.globalProductExt;
            globalProductExt.PackageDimensions = model.package_dimensions.ToJson();
            globalProductExt.PackageWeight = model.package_weight.ToJson();
            if (model.main_images != null && model.main_images.Any(image => image != null && !string.IsNullOrEmpty(image.uri)))
            {
                List<ImageUri> images = new List<ImageUri>();
                images = model.main_images.Select(i =>
                {
                    ImageUri imageUri = new ImageUri();
                    imageUri.uri = i.uri;
                    imageUri.height = 0;
                    imageUri.width = 0;
                    return imageUri;
                }).ToList();
                globalProductExt.ImageJson = images.ToJson();
            }
            else
            {
                globalProductExt.ImageJson = null;
            }
            if (model.size_chart != null)
            {
                ProductDetailSizeChart size_chart = new ProductDetailSizeChart();
                if (model.size_chart.template != null)
                {
                    size_chart.template = new GetProductDetailSizeTemplate();
                    size_chart.template.id = model.size_chart.template.id;
                }
                if (model.size_chart.image != null)
                {
                    size_chart.image = new GetGlobalProductCertificationsImages();
                    size_chart.image.height = 0;
                    size_chart.image.width = 0;
                    size_chart.image.uri = globalProductService.GetImageTkUri(model.size_chart.image.uri);
                }
                globalProductExt.SizeChart = size_chart.ToJson();
            }
            else
            {
                globalProductExt.SizeChart = null;
            }
            if (model.manufacturer != null)
            {
                GlobalCreateProductManufactoryReq manufacturer = new GlobalCreateProductManufactoryReq();
                manufacturer.name = model.manufacturer.name;
                manufacturer.address = model.manufacturer.address;
                manufacturer.phone_number = model.manufacturer.phone_number;
                manufacturer.email = model.manufacturer.email;
            }
            else
            {
                globalProductExt.Manufacturer = null;
            }
            globalProductExt.CategoryJson = new GetGlobalProductDetailCategory { id = model.category_id }.ToJson();
            if (model.product_attributes.IsNotNullAndAny())
            {
                globalProductExt.AttributeTypesJson = model.product_attributes.ToJson();
            }
            else
            {
                globalProductExt.AttributeTypesJson = null;
            }
            if (model.video != null)
            {
                //Video不存json，存id就行
                globalProductExt.Video = model.video.id;
            }
            else
            {
                globalProductExt.Video = null;
            }
            List<GlobalProductSku> globalProductSkus = new List<GlobalProductSku>();
            if (model.skus.IsNotNullAndAny())
            {
                globalProductSkus = model.skus.Select(s =>
                {
                    GlobalProductSku globalProductSku = new GlobalProductSku();
                    globalProductSku.Id = 0;
                    globalProductSku.GpUniqueCode = null;
                    globalProductSku.SkuId = s.id;
                    globalProductSku.SellerSku = s.seller_sku;
                    globalProductSku.PlatformId = s.id;
                    globalProductSku.GlobalQuantity = s.global_quantity;
                    globalProductSku.SkuUnitCount = s.sku_unit_count;
                    if (s.price != null)
                    {
                        globalProductSku.PriceAmount = !string.IsNullOrEmpty(s.price.amount) ? s.price.amount.ToDecimal() : 0;
                        globalProductSku.PriceCurrency = s.price.currency;
                    }
                    else
                    {
                        globalProductSku.PriceAmount = 0;
                        globalProductSku.PriceCurrency = null;
                    }
                    if (s.inventory.IsNotNullAndAny())
                    {
                        globalProductSku.Inventory = s.inventory.ToJson();
                    }
                    else
                    {
                        globalProductSku.Inventory = null;
                    }
                    if (s.sales_attributes.IsNotNullAndAny())
                    {
                        List<string> imageUrls = new List<string>();
                        List<GlobalProductSkuSaleAttributeRes> salesList = new List<GlobalProductSkuSaleAttributeRes>();
                        foreach (var res in s.sales_attributes)
                        {

                            GlobalProductSkuSaleAttributeRes globalProductSkuSaleAttributeRes = new GlobalProductSkuSaleAttributeRes();
                            globalProductSkuSaleAttributeRes.id = res.id;
                            globalProductSkuSaleAttributeRes.name = res.name;
                            globalProductSkuSaleAttributeRes.value_id = res.value_id;
                            globalProductSkuSaleAttributeRes.value_name = res.value_name;
                            globalProductSkuSaleAttributeRes.sku_img = new SkuImageRes();
                            globalProductSkuSaleAttributeRes.sku_img.height = 0;
                            globalProductSkuSaleAttributeRes.sku_img.width = 0;
                            if (res.sku_img != null && !string.IsNullOrEmpty(res.sku_img.uri))
                            {
                                string url = globalProductService.GetImageTkUri(res.sku_img.uri);
                                imageUrls.Add(url);
                                globalProductSkuSaleAttributeRes.sku_img.uri = url;
                            }
                            else
                            {
                                globalProductSkuSaleAttributeRes.sku_img.uri = null;
                            }

                            salesList.Add(globalProductSkuSaleAttributeRes);
                        }
                        if (imageUrls.IsNotNullAndAny())
                        {
                            globalProductSku.ImagesUrl = string.Join(",", imageUrls);
                        }
                        if (salesList.IsNotNullAndAny())
                        {
                            globalProductSku.SalesAttributes = salesList.ToJson();
                        }


                    }
                    else
                    {
                        globalProductSku.ImagesUrl = null;
                        globalProductSku.SalesAttributes = null;
                    }
                    if (s.identifier_code != null)
                    {
                        globalProductSku.Identifiercode = s.identifier_code.ToJson();
                    }
                    else
                    {
                        globalProductSku.Identifiercode = null;
                    }

                    return globalProductSku;
                }).ToList();
            }
            globalProduct.globalProductExt = globalProductExt;
            globalProduct.globalProductSkuList = globalProductSkus;
            //然后改task的状态
            taskExt.PackJson = globalProduct.ToJson();
            taskExt.UpdateTime = DateTime.Now;
            task.Ext = taskExt;
            task.UpdateTime = DateTime.Now;
            task.Status = "Created";
            task.IsResetListing = true;
            task.RetryCount = 0;
            task.ErrorCode = null;
            task.ErrorMessage = null;
            task.ExceptionMessage = null;
            task.ExcuteTime = null;
            task.SubStatus = null;
            task.FinishedTime = null;
            task.HopeTime = null;
            task.IsPublished = false;
            task.BusinessStatus = 0;
            task.BusinessRetryCount = 0;
            task.ProcessBusinessTime = null;
            task.NextProcessBusinessTime = null;
            List<ListingTaskRecords> newList = new List<ListingTaskRecords>();
            newList.Add(task);
            int count = newList.Count;
            //修改铺货日志
            try
            {
                _repository.BatchUpdate(newList);
            }
            catch (Exception ex)
            {
                count = 0;
            }

            var returnedModel = new ReturnedModel<ListingTaskStatusStat>();
            if (newList.Count > 0)
                returnedModel.Data = new ListingTaskStatusStat { BatchNo = task.BatchNo, TotalCount = newList.Count(), WaitCount = 0 };
            else
                returnedModel.Data = new ListingTaskStatusStat { BatchNo = task.BatchNo, TaskCode = task.ListingTaskCode, TotalCount = newList.Count(), WaitCount = newList.Count() };
            return returnedModel;

        }

        /// <summary>
        /// Tk在重新铺货界面需要把图片的uri转成url传给前端，让用户能看到图片，编辑传回的时候会进行转回处理，这里只处理Tk的链接，采集来的非Tk链接直接返回，在铺货的时候下载处理
        /// </summary>
        /// <param name="url"></param>
        /// <param name="shopId"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string TkPhGetImageUrl(string uri)
        {
            if (string.IsNullOrEmpty(uri))
            {
                return uri;
            }

            //以防传错，传成了url,就不处理了
            if (uri.Contains("http"))
            {
                return uri;
            }

            string url = "https://p16-oec-va.ibyteimg.com/" + uri + "~tplv-o3syd03w52-origin-image.image";
            return url;
        }

        /// <summary>
        /// 铺货日志->重新铺货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ReturnedModel<ListingTaskStatusStat> AgainListingTask(PtProductInfo model)
        {
            var returnedModel = new ReturnedModel<ListingTaskStatusStat>();
            if (string.IsNullOrWhiteSpace(model.FromCode) || model.FromType != 4)
            {
                returnedModel.Success = false;
                returnedModel.Message = "参数错误";
                return returnedModel;
            }
            if (model == null || model.Ext == null)
            {
                returnedModel.Success = false;
                returnedModel.Message = "系统错误";
                return returnedModel;
            }
            if (model.ListingShopIds == null || model.ListingShopIds.Any() == false)
            {
                returnedModel.Success = false;
                returnedModel.Message = "请选择需要铺货的店铺";
                return returnedModel;
            }

            //校验ShopId
            var fxUserShopService = new FxUserShopService();
            var fxUserShops = fxUserShopService.GetUserIdByShopId(model.ListingShopIds, "ShopId,FxUserId,NickName,PlatformType");
            //存在不属于当前用户的店铺
            if (fxUserShops == null || fxUserShops.Any() == false || fxUserShops.Any(a => a.FxUserId != model.FxUserId))
            {
                returnedModel.Success = false;
                returnedModel.Message = "店铺数据异常";
                return returnedModel;
            }
            //存在平台不一致的店铺
            if (fxUserShops == null || fxUserShops.Any(a => a.PlatformType != model.PlatformType))
            {
                returnedModel.Success = false;
                returnedModel.Message = "所选的店铺平台类型不一致";
                return returnedModel;
            }

            var logFileName = $"SaveFromPtProductInfo-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var fromBaseProductUid = model.FromBaseProductUid;
            var fromSupplierProductUid = model.FromSupplierProductUid;
            if (fromBaseProductUid == 0 && fromSupplierProductUid.HasValue && fromSupplierProductUid.Value > 0)
            {
                //校验货盘商品归属是否正确

                //通过货盘Uid查出基础商品Uid
                var fromSupplierProduct = new SupplierProductRepository().GetByUid(fromSupplierProductUid.Value);
                if (fromSupplierProduct != null && fromSupplierProduct.FxUserId == model.FromFxUserId)
                {
                    fromBaseProductUid = fromSupplierProduct.FromProductUid;
                    model.FromBaseProductUid = fromBaseProductUid;
                }
                else
                {
                    Log.Debug($"1校验货盘商品归属是否正确model.FromFxUserId={model.FromFxUserId}，fromSupplierProduct.FxUserId={fromSupplierProduct?.FxUserId}", logFileName);
                    returnedModel.Success = false;
                    returnedModel.Message = "数据异常";
                    return returnedModel;
                }
            }
            var baseProductUid = model.BaseProductUid;
            if (model.FromFxUserId == model.FxUserId)
            {
                //校验自己的基础商品归属是否正确
                var bpEnt = _baseProductService.GetByUid(baseProductUid);
                if (bpEnt == null || bpEnt.FxUserId != model.FxUserId)
                {
                    Log.Debug($"2校验自己的基础商品归属是否正确model.FromFxUserId={model.FromFxUserId}，bpEnt.FxUserId={bpEnt?.FxUserId}", logFileName);
                    returnedModel.Success = false;
                    returnedModel.Message = "数据异常";
                    return returnedModel;
                }
            }
            else if (fromBaseProductUid > 0)
            {
                //校验厂家基础商品归属是否正确
                var bpEnt = new BaseProductEntityService(model.FromFxUserId).GetByUid(fromBaseProductUid);
                if (bpEnt == null || bpEnt.FxUserId != model.FromFxUserId)
                {
                    Log.Debug($"3校验厂家基础商品归属是否正确model.FromFxUserId={model.FromFxUserId}，bpEnt.FxUserId={bpEnt?.FxUserId}", logFileName);
                    returnedModel.Success = false;
                    returnedModel.Message = "数据异常";
                    return returnedModel;
                }
            }

            var taskRecords = new List<ListingTaskRecords>();
            var taskRecordsUpdate = new List<ListingTaskRecords>();
            var ptProductInfos = new List<PtProductInfo>();
            var ptProductInfosUpdate = new List<PtProductInfo>();

            var batchNo = Guid.NewGuid().ToString().ToShortMd5();
            var shopExpressTemplates = new List<ListingTemplateGroupItem>();

            if (model.ListingConfig == null)
                model.ListingConfig = new ShopListingConfigSaveRequest();

            //批量铺货重新铺货
            var isBatchResetListing =
                model.FromCode.IsNotNullOrEmpty() &&
                model.FromType == 4 &&
                model.UserListingSetting != null &&
                model.UserListingSetting.BatchSettingValue.IsNotNullOrEmpty();

            //运费模板
            if (model.UserListingSetting != null && string.IsNullOrEmpty(model.UserListingSetting.BatchSettingValue?.HonourAgreement?.ExpressTemplateGroupCode) == false)
            {
                shopExpressTemplates = new ListingTemplateGroupItemRepository().GetTemplateGroupItemByCode
                    (model.UserListingSetting.BatchSettingValue.HonourAgreement.ExpressTemplateGroupCode, model.PlatformType) ?? new List<ListingTemplateGroupItem>();
            }

            //默认配置, 
            if (model.UserListingSetting != null && model.UserListingSetting.BatchSettingValue != null)
            {
                var lsValue = model.UserListingSetting.BatchSettingValue;
                model.ListingConfig.StockCountTime = "PayTime";
                if (lsValue.WarehouseCalcRule == WarehouseCalcRule.PlaceOrder)
                    model.ListingConfig.StockCountTime = "CreateTime";
                if (lsValue.Delivery != null)
                {
                    if (lsValue.Delivery.DeliveryTime == DeliveryTime.SameDay)
                    {
                        model.ListingConfig.PromiseDeliveryTime = 9999;
                        model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 9999,
                        } };
                    }
                    else if (lsValue.Delivery.DeliveryTime == DeliveryTime.NextDay)
                    {
                        model.ListingConfig.PromiseDeliveryTime = 1;
                        model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 1,
                        } };
                    }
                    else if (lsValue.Delivery.DeliveryTime == DeliveryTime.FortyEightHour)
                    {
                        model.ListingConfig.PromiseDeliveryTime = 2;
                        model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 2,
                        } };
                    }
                }

                if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Publish)
                {
                    //立即上架
                    model.ListingConfig.IsDirectUpload = true;
                    model.ListingConfig.StartSaleType = 0;
                }
                else if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Warehouse)
                {
                    //放入仓库
                    model.ListingConfig.IsDirectUpload = true;
                    model.ListingConfig.StartSaleType = 1;
                }
                else if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Drafts)
                {
                    //放入草稿箱
                    model.ListingConfig.IsDirectUpload = false;
                    model.ListingConfig.StartSaleType = 0;
                }
                //客服电话
                if (string.IsNullOrEmpty(model.UserListingSetting.ServiceMobile) == false)
                    model.ListingConfig.ServiceMobile = model.UserListingSetting.ServiceMobile;
                else
                    model.ListingConfig.ServiceMobile = BaseSiteContext.Current?.CurrentFxUser?.Mobile;

                // 满减折扣
                model.ListingConfig.BulkDiscount = model.UserListingSetting.BatchSettingValue.BulkDiscount;

                // 发货仓Id赋值
                model.ListingConfig.OutWarehouseId = model.UserListingSetting.BatchSettingValue.HonourAgreement?.OutWarehouseId ?? "";
            }

            //前置检查
            foreach (var shopId in model.ListingShopIds)
            {
                var curFxUserShop = fxUserShops.FirstOrDefault(a => a.ShopId == shopId);
                if (curFxUserShop == null)
                {
                    returnedModel.Success = false;
                    returnedModel.Message = "店铺数据异常";
                    return returnedModel;
                }

                var curTemplateItem = shopExpressTemplates.FirstOrDefault(a => a.ShopId == shopId);
                if (curTemplateItem == null)
                {
                    returnedModel.Success = false;
                    returnedModel.Message = $"店铺【{curFxUserShop.NickName}】未设置运费模板";
                    return returnedModel;
                }
            }

            model.IsPublic = false;
            var mainImageUrl = string.Empty;
            var fromFxUserId = model.FromFxUserId;

            if (model.Ext != null && string.IsNullOrEmpty(model.Ext.MainImageJson) == false)
            {
                var mainImages = model.Ext.MainImageJson.ToObject<List<PtProductInfoImageModel>>();
                mainImageUrl = mainImages?.OrderByDescending(a => a.IsMain).FirstOrDefault()?.ImageUrl;
            }

            var sourceJson = model.ToJson();
            var listingModel = TransferPtProductInfoToListingModel(model);
            var packJson = listingModel.ToJson();

            //铺货使用的AppKey
            var appKey = CustomerConfig.GetFxListingAppKey(model.PlatformType);

            var systemShopVersion = BaseSiteContext.Current?.CurrentLoginShop?.Version;
            if (CustomerConfig.IsDebug)
            {
                systemShopVersion += "t"; // 测试环境，加一个t，用于识别
            }

            //一个铺货店铺对应一条数据（铺货任务+平台资料草稿）
            model.ListingShopIds.ForEach(shopId =>
            {
                var curTask = new ListingTaskRecords();
                curTask.Ext = new ListingTaskRecordsExt();
                var curTemplateItem = shopExpressTemplates.FirstOrDefault(a => a.ShopId == shopId);
                {
                    // 任务更新
                    curTask = _repository.GetByCode(model.FromCode);
                    var curPtProductInfo = _ptProductInfoService.GetByCode(curTask.PtProductUniqueCode);
                    if (curTask != null && curPtProductInfo != null)
                    {
                        curTask.UpdateTime = DateTime.Now;
                        curTask.Status = "Created";
                        curTask.IsResetListing = true;
                        curTask.RetryCount = 0;
                        curTask.AppKey = appKey;
                        curTask.ErrorCode = null;
                        curTask.ErrorMessage = null;
                        curTask.ExceptionMessage = null;
                        curTask.ExcuteTime = null;
                        curTask.SubStatus = null;
                        curTask.FinishedTime = null;
                        curTask.HopeTime = null;
                        curTask.IsPublished = false;
                        curTask.SourceVersion = systemShopVersion;
                        curTask.ImageUrl = mainImageUrl;
                        curTask.FromFxUserId = fromFxUserId;

                        curTask.ShopId = shopId;
                        curTask.BaseProductUId = model.BaseProductUid;
                        curTask.SupplierProductUId = model.FromSupplierProductUid.HasValue ? model.FromSupplierProductUid.Value : 0;
                        curTask.ProductName = model.Subject;
                        curTask.TargetShopId = shopId;
                        curTask.TargetPlatformType = model.PlatformType;

                        curTask.BusinessStatus = 0;
                        curTask.BusinessRetryCount = 0;
                        curTask.ProcessBusinessTime = null;
                        curTask.NextProcessBusinessTime = null;

                        var curListingModel = packJson.ToObject<PlatformListingModel>();
                        if (curListingModel == null)
                            curListingModel = new PlatformListingModel();
                        curListingModel.TargetPlatformType = model.PlatformType;
                        curListingModel.TargetShopId = shopId;
                        curListingModel.TargetPlatform = CustomerConfig.GetCloudPlatformType(model.PlatformType);

                        //运费模板
                        if (curListingModel.ListingConfig == null)
                            curListingModel.ListingConfig = new ShopListingConfigSaveRequest();
                        curListingModel.ListingConfig.PostageId = curTemplateItem?.PlatformTemplateId ?? "";
                        curListingModel.ListingConfig.PostageName = curTemplateItem?.PlatformTemplateName ?? "";

                        curTask.Ext.PackJson = curListingModel.ToJson();
                        curTask.Ext.UpdateTime = DateTime.Now;

                        //资料更新
                        var modelProductInfo = sourceJson.ToObject<PtProductInfo>();

                        curPtProductInfo.ShopId = shopId;
                        curPtProductInfo.Status = -1;//平台资料状态为草稿

                        curPtProductInfo.IsPublic = modelProductInfo.IsPublic;
                        curPtProductInfo.Subject = modelProductInfo.Subject;
                        curPtProductInfo.CategoryId = modelProductInfo.CategoryId;
                        curPtProductInfo.FreightId = modelProductInfo.FreightId;
                        curPtProductInfo.UpdateTime = DateTime.Now;
                        if (curPtProductInfo.Ext == null)
                            curPtProductInfo.Ext = new PtProductInfoExt();
                        curPtProductInfo.Ext.PtProductUniqueCode = curPtProductInfo.UniqueCode;
                        curPtProductInfo.Ext.CategoryJson = modelProductInfo.Ext.CategoryJson;
                        curPtProductInfo.Ext.MainImageJson = modelProductInfo.Ext.MainImageJson;
                        curPtProductInfo.Ext.Description = modelProductInfo.Ext.Description;
                        curPtProductInfo.Ext.CategoryAttributes = modelProductInfo.Ext.CategoryAttributes;
                        curPtProductInfo.Ext.SkuJson = modelProductInfo.Ext.SkuJson;
                        curPtProductInfo.Ext.UpdateTime = DateTime.Now;

                        taskRecordsUpdate.Add(curTask);
                        ptProductInfosUpdate.Add(curPtProductInfo);
                    }
                }
       
                #region 商品铺货状态转换赋值，铺货设置统一走多商品的配置，ListingSettingValue 这里存的多商品的配置值(PtProductDatas回显赋值是多商品配置)
                if (model.UserListingSetting != null && model.UserListingSetting.BatchSettingValue != null)
                {
                    // 立即上架
                    if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Publish)
                        curTask.ListingProductStatus = (int)ListingProductStatusEnum.ReadyForSaleAudit;

                    // 放入仓库
                    if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Warehouse)
                        curTask.ListingProductStatus = (int)ListingProductStatusEnum.PutStashAudit;

                    // 放入草稿箱
                    if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Drafts)
                        curTask.ListingProductStatus = (int)ListingProductStatusEnum.DraftIn;
                }
                #endregion
            });
            if (ptProductInfos.Count > 0)
                _ptProductInfoService.BatchAdd(ptProductInfos);
            if (ptProductInfosUpdate.Count > 0)
                _ptProductInfoService.BatchUpdate(ptProductInfosUpdate);
            if (taskRecords.Count > 0)
                _repository.BatchAdd(taskRecords);
            if (taskRecordsUpdate.Count > 0)
                _repository.BatchUpdate(taskRecordsUpdate);

            var isbatch = isBatchResetListing ? true : false;

            // //保存铺货默认配置
            // if (model.UserListingSetting != null && model.UserListingSetting.IsDefault)
            // {
            //     _userListingSettingService.SaveOrUpdate(model.UserListingSetting, isbatch);
            // }
            returnedModel.Success = true;
            returnedModel.Message = "";

            if (taskRecords.Count > 0)
                returnedModel.Data = new ListingTaskStatusStat { BatchNo = batchNo, TotalCount = taskRecords.Count(), WaitCount = taskRecords.Count() };
            else
                returnedModel.Data = new ListingTaskStatusStat { BatchNo = batchNo, TaskCode = taskRecordsUpdate.FirstOrDefault()?.ListingTaskCode, TotalCount = taskRecordsUpdate.Count(), WaitCount = taskRecordsUpdate.Count() };

            return returnedModel;
        }

        /// <summary>
        /// 批量保存：铺货任务+店铺平台资料草稿
        /// </summary>
        /// <param name="models"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        public ReturnedModel<BatchListingTaskRes> SaveFromPtProductInfos(List<PtProductInfo> models, BatchListingTaskModel req, int fxUserId)
        {
            var returnedModel = new ReturnedModel<BatchListingTaskRes>() { Data = new BatchListingTaskRes() };
            var ptProductInfoSuccessModels = new List<PtProductInfo>();
            var ptProductInfoFailModels = new List<PtProductInfo>();
            var failModelUniqueCodes = new List<string>();
            var shopExpressTemplates = new List<ListingTemplateGroupItem>();

            var fxUserShopService = new FxUserShopService();
            var fxUserShops = fxUserShopService.GetUserIdByShopId(req.ListingShopIds, "ShopId,FxUserId,NickName,PlatformType");

            if (fxUserShops == null || !fxUserShops.Any() || fxUserShops.Any(a => a.FxUserId != fxUserId))
            {
                // 存在不属于当前用户的店铺
                returnedModel.Success = false;
                returnedModel.Message = "店铺数据异常";
                returnedModel.Data.SuccessCount = 0;
                returnedModel.Data.FailCount = 0;
                return returnedModel;
            }
            if (fxUserShops == null || fxUserShops.Any(a => a.PlatformType != req.PlatformType))
            {
                // 存在平台不一致的店铺
                returnedModel.Success = false;
                returnedModel.Message = "所选的店铺平台类型不一致";
                returnedModel.Data.SuccessCount = 0;
                returnedModel.Data.FailCount = 0;
                return returnedModel;
            }

            // 铺货运费模板（批量）
            foreach (var model in models)
            {
                model.ListingShopIds = req.ListingShopIds;
                model.UserListingSetting = model.Ext.SettingJson.ToObject<ListingSetting>();
            }

            // 运费模板
            var expressTemplateGroupCodes = models
                .Select(p => p.UserListingSetting?.BatchSettingValue?.HonourAgreement?.ExpressTemplateGroupCode)
                .Where(p => !string.IsNullOrEmpty(p)).Distinct().ToList();
            if (expressTemplateGroupCodes.Count > 0)
            {
                shopExpressTemplates = new ListingTemplateGroupItemRepository()
                    .GetTemplateGroupItemByCodes(expressTemplateGroupCodes, req.PlatformType);
            }

            // 铺货资料检测
            CheckListParms(models, req, ptProductInfoFailModels, shopExpressTemplates, fxUserShops);

            failModelUniqueCodes = ptProductInfoFailModels.Select(p => p.UniqueCode).Distinct().ToList();

            ptProductInfoSuccessModels = models.Where(p => !failModelUniqueCodes.Contains(p.UniqueCode)).ToList();

            var batchTaskRecords = new List<ListingTaskRecords>();
            var batchPtProductInfos = new List<PtProductInfo>();

            foreach (var model in ptProductInfoSuccessModels)
            {
                // 一个商品一批
                var taskBatchNo = Guid.NewGuid().ToString().ToShortMd5();
                var taskRecords = new List<ListingTaskRecords>();
                var ptProductInfos = new List<PtProductInfo>();
                model.IsPublic = false;
                try
                {
                    var mainImageUrl = string.Empty;
                    var mainImages = new List<PtProductInfoImageModel>();
                    var fromFxUserId = model.FromFxUserId;

                    if (model.Ext != null && string.IsNullOrEmpty(model.Ext.MainImageJson) == false)
                    {
                        mainImages = model.Ext.MainImageJson.ToObject<List<PtProductInfoImageModel>>();
                        mainImageUrl = mainImages?.OrderByDescending(a => a.IsMain).FirstOrDefault()?.ImageUrl;
                    }
                    var sourceJson = model.ToJson();
                    var listingModel = TransferPtProductInfoToListingModel(model);
                    var packJson = listingModel.ToJson();

                    //铺货使用的AppKey
                    var appKey = CustomerConfig.GetFxListingAppKey(model.PlatformType);

                    var systemShopVersion = BaseSiteContext.Current?.CurrentLoginShop?.Version;
                    if (CustomerConfig.IsDebug)
                        systemShopVersion += "t"; // 测试环境，加一个t，用于识别

                    var currentShopExpressTemplates = shopExpressTemplates
                      .Where(p => p.GroupUniqueCode == model.UserListingSetting
                      ?.BatchSettingValue?.HonourAgreement?.ExpressTemplateGroupCode).ToList();

                    //一个铺货店铺对应一条数据（铺货任务+平台资料草稿）
                    model.ListingShopIds.ForEach(shopId =>
                    {
                        var curTask = new ListingTaskRecords
                        {
                            Ext = new ListingTaskRecordsExt()
                        };
                        var curTemplateItem = currentShopExpressTemplates.FirstOrDefault(a => a.ShopId == shopId);

                        // 资料创建
                        var uniqueCode = Guid.NewGuid().ToString().ToShortMd5();
                        var taskCode = (Guid.NewGuid().ToString() + uniqueCode + shopId).ToShortMd5();
                        var curPtProductInfo = sourceJson.ToObject<PtProductInfo>();
                        curPtProductInfo.ShopId = shopId;
                        curPtProductInfo.Status = -1;//平台资料状态为草稿
                        curPtProductInfo.UniqueCode = uniqueCode;
                        if (curPtProductInfo.Ext == null)
                            curPtProductInfo.Ext = new PtProductInfoExt();
                        curPtProductInfo.Ext.PtProductUniqueCode = uniqueCode;

                        // 任务创建
                        curTask.ListingTaskCode = taskCode;
                        curTask.BatchNo = taskBatchNo;
                        curTask.PtProductUniqueCode = uniqueCode;
                        curTask.FxUserId = model.FxUserId;
                        curTask.ShopId = shopId;
                        curTask.CreateTime = DateTime.Now;
                        curTask.UpdateTime = DateTime.Now;
                        curTask.BaseProductUId = model.BaseProductUid;
                        curTask.SupplierProductUId = model.FromSupplierProductUid.HasValue ? model.FromSupplierProductUid.Value : 0;
                        curTask.ProductName = model.Subject;
                        curTask.TargetShopId = shopId;
                        curTask.TargetPlatformType = model.PlatformType;
                        curTask.Status = "Created";
                        curTask.AppKey = appKey;
                        curTask.IsPublished = false;
                        curTask.SourceVersion = systemShopVersion;
                        curTask.ImageUrl = mainImageUrl;
                        curTask.FromFxUserId = fromFxUserId;

                        var curListingModel = packJson.ToObject<PlatformListingModel>();
                        if (curListingModel == null)
                            curListingModel = new PlatformListingModel();
                        curListingModel.TargetPlatformType = model.PlatformType;
                        curListingModel.TargetShopId = shopId;
                        curListingModel.TargetPlatform = CustomerConfig.GetCloudPlatformType(model.PlatformType);

                        //运费模板
                        if (curListingModel.ListingConfig == null)
                            curListingModel.ListingConfig = new ShopListingConfigSaveRequest();
                        curListingModel.ListingConfig.PostageId = curTemplateItem?.PlatformTemplateId ?? "";
                        curListingModel.ListingConfig.PostageName = curTemplateItem?.PlatformTemplateName ?? "";

                        curTask.Ext.ListingTaskCode = taskCode;
                        curTask.Ext.PackJson = curListingModel.ToJson();
                        curTask.Ext.CreateTime = DateTime.Now;
                        curTask.Ext.UpdateTime = DateTime.Now;

                        #region 商品铺货状态转换赋值，商品铺货状态-赋初值：BatchSettingValue
                        if (model.UserListingSetting != null && model.UserListingSetting.BatchSettingValue != null)
                        {
                            // 立即上架
                            if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Publish)
                                curTask.ListingProductStatus = (int)ListingProductStatusEnum.ReadyForSaleAudit;

                            // 放入仓库
                            if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Warehouse)
                                curTask.ListingProductStatus = (int)ListingProductStatusEnum.PutStashAudit;

                            // 放入草稿箱
                            if (model.UserListingSetting.BatchSettingValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Drafts)
                                curTask.ListingProductStatus = (int)ListingProductStatusEnum.DraftIn;
                        }
                        #endregion

                        #region 检测是否有重复铺货
                        var isRepeat = CheckRepeatPtListingV2(shopId, model);
                        if (isRepeat)
                        {
                            curTask.Status = "Error_Repeat";
                            curTask.ErrorMessage = "来源商品铺货过，已为您自动过滤";
                        }
                        #endregion

                        if (model.PlatformType == PlatformType.TouTiaoSaleShopSupply.ToString() && model.CreateFrom == CreateFromType.SupplierProductSaleShop)
                        {
                            CheckWarehouseCity(model, curTask);
                        }

                        ptProductInfos.Add(curPtProductInfo);
                        taskRecords.Add(curTask);
                    });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"铺货资料过滤：{model.UniqueCode} 铺货任务组装异常:{ex.ToJson()}", ModuleFileName.SaveFromPtProductInfos);
                    ptProductInfoFailModels.Add(model);
                }

                if (ptProductInfos.Count > 0)
                    batchPtProductInfos.AddRange(ptProductInfos);
                if (taskRecords.Count > 0)
                    batchTaskRecords.AddRange(taskRecords);
            }

            if (batchPtProductInfos.Count > 0)
                _ptProductInfoService.BatchAdd(batchPtProductInfos);
            if (batchTaskRecords.Count > 0)
                _repository.BatchAdd(batchTaskRecords);

            returnedModel.Success = true;
            returnedModel.Message = "成功";
            returnedModel.Data.SuccessCount = ptProductInfoSuccessModels.Count;
            returnedModel.Data.FailCount = ptProductInfoFailModels.Count;
            return returnedModel;
        }

        private void CheckWarehouseCity(PtProductInfo model, ListingTaskRecords curTask)
        {
            // 铺货时，需校验厂家货源商品的发货仓地址的【市】与铺货设置所选择的店铺的【发货仓】的【市】是否一致
            // 不一致:铺货日志直接显示失败，失败原因：货源发货仓地址与店铺发货仓地址【城市】不一致，不能铺货。且不显示【重新铺货】按钮
            var shop = SiteContext.Current.UserShops.Find(x => x.ShopId == model.ShopId.ToString());
            var ptWarehouseData = new WarehouseHander().GetPtWarehouseData(model.PlatformType, shop);

            long cityCode = ptWarehouseData.Find(r => r.out_warehouse_id == model.ListingConfig.OutWarehouseId).warehouse_location.address_id2;
            string ptCityName = new PlatformAreaCodeInfoService().PlatformAreaByCode(cityCode, model.PlatformType)?.Name ?? "";

            // addressCode、supplierAddress 不再判空，需要报错暴露出来
            var addressCode = new SupplierProductRepository().GetByUid(model.FromSupplierProductUid.Value)?.AddressCode ?? "";
            if (!string.IsNullOrWhiteSpace(addressCode))
            {
                var supplierAddress = new SupplierAddressService().GetByAddressCode(addressCode);
                if (supplierAddress.CityName != ptCityName)
                {
                    curTask.Status = "Error_Repeat";
                    curTask.ErrorMessage = "货源发货仓地址与店铺发货仓地址【城市】不一致";

                    Log.DebugMsg($"发货失败，货源地址不一致，平台城市名称:{ptCityName},用户选择的:{supplierAddress?.CityName ?? ""}", LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                }
            }
        }

        private void CheckListParms(List<PtProductInfo> models, BatchListingTaskModel req, List<PtProductInfo> ptProductInfoFailModels, List<ListingTemplateGroupItem> shopExpressTemplates, List<FxUserShop> fxUserShops)
        {
            var platformCategoryService = new PlatformCategoryService();
            foreach (var model in models)
            {
                if (model.ListingConfig == null) model.ListingConfig = new ShopListingConfigSaveRequest();

                if (model == null || model.Ext == null)
                {
                    Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 扩展数据为空",LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                    ptProductInfoFailModels.Add(model);
                    continue;
                }

                var baseProductUid = model.BaseProductUid;
                var fromBaseProductUid = model.FromBaseProductUid;
                var fromSupplierProductUid = model.FromSupplierProductUid ?? 0;

                #region 商品归属检测
                if (model.CreateFrom == "SelfBaseProduct")
                {
                    // 来源自己基础商品
                    var product = _baseProductService.GetByUid(baseProductUid);
                    if (product == null || product.FxUserId != model.FxUserId)
                    {
                        Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 自己的基础商品归属不正确",
                                  LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                        ptProductInfoFailModels.Add(model);
                        continue;
                    }
                }
                else
                {
                    // 来源自己货盘
                    if (model.CreateFrom == "SelfSupplierProduct")
                    {
                        if (fromBaseProductUid > 0)
                        {
                            var product = new BaseProductEntityService(model.FromFxUserId).GetByUid(fromBaseProductUid);
                            if (product == null || product.FxUserId != model.FromFxUserId)
                            {
                                Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 自己货盘基础商品归属不正确",
                                   LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                                ptProductInfoFailModels.Add(model);
                                continue;
                            }
                        }
                    }
                    // 来源厂家货盘
                    if (model.CreateFrom == "SupplierProduct")
                    {
                        if (fromSupplierProductUid > 0)
                        {
                            var fromSupplierProduct = new SupplierProductRepository().GetByUid(fromSupplierProductUid);
                            if (fromSupplierProduct == null || fromSupplierProduct.FxUserId != model.FromFxUserId)
                            {
                                Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 厂家货盘商品归属不正确",
                                    LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                                ptProductInfoFailModels.Add(model);
                                continue;
                            }
                        }
                        if (fromBaseProductUid > 0)
                        {
                            var product = new BaseProductEntityService(model.FromFxUserId).GetByUid(fromBaseProductUid);
                            if (product == null || product.FxUserId != model.FromFxUserId)
                            {
                                Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 厂家货盘基础商品归属不正确",
                                    LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                                ptProductInfoFailModels.Add(model);
                                continue;
                            }
                        }
                    }
                }
                #endregion

                #region 运费模板检测(店铺)
                var currentExpressTemplateGroupCode = model.UserListingSetting?.BatchSettingValue?.HonourAgreement?.ExpressTemplateGroupCode;
                var currentShopExpressTemplates = shopExpressTemplates.Where(p => p.GroupUniqueCode == currentExpressTemplateGroupCode).ToList();
                var skipOuter = false;
                foreach (var shopId in model.ListingShopIds)
                {
                    var curFxUserShop = fxUserShops.FirstOrDefault(a => a.ShopId == shopId);
                    var curTemplateItem = currentShopExpressTemplates.FirstOrDefault(a => a.ShopId == shopId);
                    if (curTemplateItem == null)
                    {
                        Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 店铺【{curFxUserShop.NickName}】未设置运费模板",
                            LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                        skipOuter = true;
                        break;
                    }
                }
                if (skipOuter)
                {
                    ptProductInfoFailModels.Add(model);
                    continue;
                }
                #endregion

                #region 发货仓必填检测
                if (model.PlatformType == PlatformType.TouTiaoSaleShopSupply.ToString())
                {
                    string warehouseCode = model.UserListingSetting?.BatchSettingValue?.HonourAgreement?.OutWarehouseId;
                    var skipOuter_warehouseList = false;
                    foreach (var shopId in model.ListingShopIds)
                    {
                        var curFxUserShop = fxUserShops.Find(a => a.ShopId == shopId);
                        if (string.IsNullOrWhiteSpace(warehouseCode))
                        {
                            Log.DebugMsg($"铺货资料过滤：{model.UniqueCode} 店铺【{curFxUserShop.NickName}】未设置发货仓",
                                LogModuleTypeEnum.SaveFromPtProductInfos, ModuleFileName.SaveFromPtProductInfos);
                            skipOuter_warehouseList = true;
                            break;
                        }
                    }
                    if (skipOuter_warehouseList)
                    {
                        ptProductInfoFailModels.Add(model);
                        continue;
                    }
                }
                #endregion

                #region 铺货工具数据模型基础字段赋值
                if (model.UserListingSetting != null && model.UserListingSetting.BatchSettingValue != null)
                {
                    var lsValue = model.UserListingSetting.BatchSettingValue;
                    model.ListingConfig.StockCountTime = "PayTime";
                    if (lsValue.WarehouseCalcRule == WarehouseCalcRule.PlaceOrder)
                        model.ListingConfig.StockCountTime = "CreateTime";
                    if (lsValue.Delivery != null)
                    {
                        if (lsValue.Delivery.DeliveryTime == DeliveryTime.SameDay)
                        {
                            model.ListingConfig.PromiseDeliveryTime = 9999;
                            model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 9999,
                        } };
                        }
                        else if (lsValue.Delivery.DeliveryTime == DeliveryTime.NextDay)
                        {
                            model.ListingConfig.PromiseDeliveryTime = 1;
                            model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 1,
                        } };
                        }
                        else if (lsValue.Delivery.DeliveryTime == DeliveryTime.FortyEightHour)
                        {
                            model.ListingConfig.PromiseDeliveryTime = 2;
                            model.ListingConfig.TimeLinessList = new List<SpecsItem> {
                            new SpecsItem {
                                IsPresellSpec = false,//是否预售=false
                                Days = 2,
                        } };

                        }
                    }

                    if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Publish)
                    {
                        //立即上架
                        model.ListingConfig.IsDirectUpload = true;
                        model.ListingConfig.StartSaleType = 0;
                    }
                    else if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Warehouse)
                    {
                        //放入仓库
                        model.ListingConfig.IsDirectUpload = true;
                        model.ListingConfig.StartSaleType = 1;
                    }
                    else if (lsValue.ProductStatus == Data.FxModel.Listing.ProductStatus.Drafts)
                    {
                        //放入草稿箱
                        model.ListingConfig.IsDirectUpload = false;
                        model.ListingConfig.StartSaleType = 0;
                    }
                    //客服电话
                    if (string.IsNullOrEmpty(model.UserListingSetting.ServiceMobile) == false)
                        model.ListingConfig.ServiceMobile = model.UserListingSetting.ServiceMobile;
                    else
                        model.ListingConfig.ServiceMobile = BaseSiteContext.Current?.CurrentFxUser?.Mobile;

                    // 满减折扣
                    model.ListingConfig.BulkDiscount = model.UserListingSetting.BatchSettingValue.BulkDiscount;

                    // 发货仓Id赋值
                    model.ListingConfig.OutWarehouseId = model.UserListingSetting.BatchSettingValue.HonourAgreement?.OutWarehouseId ?? "";
                }

                if (model.PlatformType == PlatformType.KuaiShou.ToString())
                {
                    // 七天无理由：、坏了包退、过敏包退、破损包退、查询到其他服务，如果是必须，则直接传入。如果是可选、可不选，则统一不选
                    var categoryProps = platformCategoryService.GetPrimevalCategoryProps(new GetCategoryPropReqModel { PlatformType = req.PlatformType, EndCateId = model.CategoryId });
                    var categoryProp = categoryProps.FirstOrDefault();
                    var listingCategoryConfig = categoryProp.ToObject<ListingCategoryConfig>();
                    if (listingCategoryConfig != null)
                    {
                        model.ListingConfig.AllergyRefund = listingCategoryConfig.categoryConfig?.allergyRefund; // 过敏包退
                        model.ListingConfig.BrokenRefund = listingCategoryConfig.categoryConfig?.brokenRefund; // 破损包退
                        model.ListingConfig.IfreshRotRefund = listingCategoryConfig.categoryConfig?.freshRotRefund; // 坏了包退
                        model.ListingConfig.WeightGuarantee = listingCategoryConfig.categoryConfig?.weightGuarantee; // 足斤足两
                    }
                }
                #endregion
            }
        }

        /*
        /// <summary>
        /// 检测重复铺货
        /// </summary>
        /// <returns></returns>
        public bool CheckRepeatPtListing(int shopId, PtProductInfo model)
        {
            var fxUserId = model.FxUserId;
            var baseProductRepository = new BaseProductRepository(fxUserId);
            var repository = new PtProductInfoRepository(fxUserId);

            var isOneSelf = model.FxUserId == model.FromFxUserId;
            if (isOneSelf)
            {
                var baseProductUid = model.FromBaseProductUid;
                // 根据基础商品来源Id——>找到厂家的【基础商品】，判断厂家基础商品是否有铺货过
                BaseProductEntity baseProduct = baseProductRepository.GetByUid(model.FromBaseProductUid); // 找到厂家的基础商品
                if (baseProduct != null)
                    baseProductUid = GetSourceTracingUid(baseProduct);

                // List<string> ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromBaseProductUid: baseProductUid);

                List<string> ptcode = new List<string>();
                if (baseProduct.FromProductUid.IsNotNullOrEmpty())
                    ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromSupplierProductUid: baseProductUid);
                else
                    ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromBaseProductUid: baseProductUid);
                
                return _repository.ExistListingTaskRecords(shopId, ptcode);
            }
            else
            {
                var fromSupplierProductUid = model.FromSupplierProductUid ?? 0;
                // 根据厂家货盘Id—>找到当前用户的【基础商品】，判断当前用户的基础商品是否有铺货过
                BaseProductEntity baseProduct = baseProductRepository.GetExistByFromProductUid(model.FromSupplierProductUid.ToString(), fxUserId);
                if (baseProduct != null)
                {
                    var baseProductUid = GetSourceTracingUid(baseProduct);

                    //List<string> t_ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromBaseProductUid: baseProductUid);

                    List<string> t_ptcode = new List<string>();
                    if (baseProduct.FromProductUid.IsNotNullOrEmpty())
                        t_ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromSupplierProductUid: baseProductUid);
                    else
                        t_ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromBaseProductUid: baseProductUid);

                    return _repository.ExistListingTaskRecords(shopId, t_ptcode);
                }

                List<string> ptcode = repository.GetPtProductUniqueCode(model.FxUserId, fromSupplierProductUid: fromSupplierProductUid);
                return _repository.ExistListingTaskRecords(shopId, ptcode);
            }
        }
        */

        public bool CheckRepeatPtListingV2(int shopId, PtProductInfo model)
        {
            bool islog = CustomerConfig.IsDebug;
            var baseProductRepository = new BaseProductRepository(model.FxUserId);
            var repository = new PtProductInfoRepository(model.FxUserId);
            StringBuilder logs = new StringBuilder();
            List<string> ptcode = new List<string>();
            var isOneSelf = model.FxUserId == model.FromFxUserId;
            if (islog) logs.AppendLine($"\n①. isOneSelf = {isOneSelf} ptcode={model.UniqueCode} shopId={shopId} ");
            if (isOneSelf)
            {
                // 根据基础商品来源Id——>找到厂家的【基础商品】，判断厂家基础商品是否有铺货过
                BaseProductEntity baseProduct = baseProductRepository.GetByUid(model.FromBaseProductUid); // 找到厂家的基础商品
                if (baseProduct == null)
                {
                    return false; // 异常数据
                }

                if (islog) logs.AppendLine($"②. model.FromBaseProductUid = {model.FromBaseProductUid} baseProduct.FromProductUid={baseProduct?.FromProductUid ?? ""} baseProduct.Uid={baseProduct?.Uid}  ");
                long fpuid; long.TryParse(baseProduct.FromProductUid, out fpuid);
                ptcode = repository.GetPtProductUniqueCode2(model.FxUserId, shopId, fpuid, baseProduct.Uid, islog);
                if (islog) logs.AppendLine($"③. model.FxUserId = {model.FxUserId} ptcode={ptcode.ToJson()}");
            }
            else
            {
                var fromSupplierProductUid = model.FromSupplierProductUid ?? 0;
                // 根据厂家货盘Id—>找到当前用户的【基础商品】，判断当前用户的基础商品是否有铺货过
                BaseProductEntity baseProduct = baseProductRepository.GetExistByFromProductUid(fromSupplierProductUid.ToString(), model.FxUserId);
                if (baseProduct == null)
                {
                    ptcode = repository.GetPtProductUniqueCode2(model.FxUserId, shopId, fromSupplierProductUid: fromSupplierProductUid, islog: islog);
                    if (islog) logs.AppendLine($"④. model.FxUserId = {model.FxUserId} fromSupplierProductUid={fromSupplierProductUid} ptcode={ptcode.ToJson()}");
                }
                else
                {
                    if (islog) logs.AppendLine($"⑤. model.FxUserId = {model.FxUserId} baseProduct.FromProductUid={baseProduct?.FromProductUid ?? ""} baseProduct.Uid={baseProduct?.Uid} ");
                    long fpuid; long.TryParse(baseProduct.FromProductUid, out fpuid);
                    ptcode = repository.GetPtProductUniqueCode2(model.FxUserId, shopId, fpuid, baseProduct.Uid, islog);
                    if (islog) logs.AppendLine($"⑥. ptcode={ptcode.ToJson()}");
                }
            }

            var result = _repository.ExistListingTaskRecords(shopId, ptcode, islog);
            if (islog) logs.AppendLine($"⑦. resl={result} 结束");

            if (islog) Log.WriteLine($"铺货检测日志：{logs}", "CheckRepeatPtListing.txt");
            return result;
        }

        private long GetSourceTracingUid(BaseProductEntity baseProduct)
        {
            long baseProductUid;
            if (long.TryParse(baseProduct.FromProductUid, out long v))
                baseProductUid = v; // 根据基础商品来源Id——>找到自己的【基础商品】，判断当前基础商品的“来源基础商品”是否铺货过
            else
                baseProductUid = baseProduct.Uid;  // 根据基础商品来源Id——>找到自己的【基础商品】，判断当前基础商品是否有铺货过

            return baseProductUid;
        }

        /// <summary>
        /// 平台资料转为铺货任务Model
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PlatformListingModel TransferPtProductInfoToListingModel(PtProductInfo model)
        {
            if (model == null || model.Ext == null)
                return null;

            var cateItems = new List<CateItem>();
            if (string.IsNullOrEmpty(model.Ext.CategoryJson) == false)
            {
                cateItems = model.Ext.CategoryJson.ToObject<List<CateItem>>();
            }
            var listingConfig = model.ListingConfig;
            if (listingConfig == null)
            {
                listingConfig = new ShopListingConfigSaveRequest();
            }

            listingConfig.ShopId = model.ShopId;
            listingConfig.CateList = cateItems;

            var listingModel = new PlatformListingModel();
            listingModel.TargetPlatformType = model.PlatformType;
            listingModel.TargetShopId = model.ShopId;
            listingModel.FirstCate = cateItems?.FirstOrDefault();
            listingModel.LastCate = cateItems?.LastOrDefault();
            listingModel.ListingConfig = listingConfig;

            var curModel = model.ToJson().ToObject<PtProductInfo>();
            //图片处理
            var preUrl = CustomerConfig.AlibabaFenFaSystemUrl + "/ImageFile/Get?objectKey=";
            if (curModel.Ext != null && string.IsNullOrEmpty(curModel.Ext.Description) == false)
            {
                var arrDesc = curModel.Ext.Description.Split(',');
                var descImages = new List<string>();
                foreach (var img in arrDesc)
                {
                    var curPath = ImgHelper.ChangeImgUrl(img, preUrl);
                    descImages.Add(curPath);
                }
                curModel.Ext.Description = string.Join(",", descImages);
            }
            if (curModel.Ext != null && string.IsNullOrEmpty(curModel.Ext.MainImageJson) == false)
            {
                var mainImages = curModel.Ext.MainImageJson.ToObject<List<PtProductInfoImageModel>>();
                if (mainImages != null)
                {
                    foreach (var img in mainImages)
                    {
                        var curPath = ImgHelper.ChangeImgUrl(img.ImageUrl, preUrl);
                        img.ImageUrl = curPath;
                    }
                }
                curModel.Ext.MainImageJson = mainImages.ToJson();
            }

            if (curModel.Ext != null && string.IsNullOrEmpty(curModel.Ext.SkuJson) == false)
            {
                var skus = curModel.Ext.SkuJson.ToObject<List<PtProductInfoSkuModel>>();
                if (skus != null)
                {
                    foreach (var sku in skus)
                    {
                        var curPath = ImgHelper.ChangeImgUrl(sku.ImageUrl, preUrl);
                        sku.ImageUrl = curPath;
                    }
                }
                curModel.Ext.SkuJson = skus.ToJson();
            }

            //类目属性简化，移除Options及Rule的值
            if (curModel.Ext != null && string.IsNullOrEmpty(curModel.Ext.CategoryAttributes) == false)
            {
                try
                {
                    var cateProps = curModel.Ext.CategoryAttributes.ToObject<List<ListingProductEditResultModelByCateProps>>();
                    cateProps?.ForEach(cp =>
                    {
                        cp.Options = null;
                        cp.Rule = null;
                    });
                    curModel.Ext.CategoryAttributes = cateProps?.ToJson();
                }
                catch (Exception ex) { }
            }

            curModel.ListingConfig = null;

            listingModel.PtProductInfo = curModel;

            return listingModel;
        }


        /// <summary>
        /// 统计任务执行状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ListingTaskStatusStat StatusStat(ListingTaskStatusQuery model)
        {
            if (model.FxUserId <= 0)
                return new ListingTaskStatusStat();
            if (string.IsNullOrEmpty(model.BatchNo) && string.IsNullOrEmpty(model.TaskCode))
                return new ListingTaskStatusStat();

            // 获取任务信息
            var list = GetTaskStatus(model);
            var result = new ListingTaskStatusStat();
            if (list == null || list.Any() == false)
                return result;
            result.TotalCount = list.Count();
            result.SuccessCount = list.Where(a => a.Status.ToString2().ToLower() == "success").Count();
            result.FailCount = list.Where(a => a.Status.ToString2().ToLower() == "error").Count();
            result.WaitCount = list.Where(a => a.Status.ToString2().ToLower() == "created" || a.Status.ToString2() == "").Count();
            result.DoingCount = list.Where(a => a.Status.ToString2().ToLower() == "doing").Count();
            result.BatchNo = model.BatchNo;
            result.TaskCode = model.TaskCode;
            return result;
        }

        /// <summary>
        /// 查询任务状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<ListingTaskRecords> GetTaskStatus(ListingTaskStatusQuery model)
        {
            return _repository.GetTaskStatus(model);
        }

        #region 铺货任务-相关业务逻辑处理

        /// <summary>
        /// （铺货任务完成后）处理业务逻辑
        /// </summary>
        /// <param name="code"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ReturnedModel<List<ListingTaskBusinessResult>> ProcessListingTaskBusiness(string code, int fxUserId)
        {
            var result = new ReturnedModel<List<ListingTaskBusinessResult>>();
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                result.Success = false;
                result.Message = "只能在精选云执行";
                return result;
            }
            var model = GetByCode(code, fxUserId);

            if (model == null)
            {
                result.Success = false;
                result.Message = "查无相关数据";
                return result;
            }
            if (model.Status.ToString2().ToLower() != "success")
            {
                result.Success = false;
                result.Message = "铺货任务状态错误";
                return result;
            }
            if (model.BusinessStatus != 0 && model.BusinessStatus != -1 && CustomerConfig.IsDebug == false)
            {
                result.Success = false;
                result.Message = "逻辑处理状态错误";
                return result;
            }

            #region 业务日志1，处理前的数据
            var methodName = "ProcessListingTaskBusiness";
            var businessLogs = new List<BusinessLogModel> { new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = "",
                    SourceType = "铺货任务-处理相关业务逻辑-开始",
                    PlatformType = model.TargetPlatformType,
                    CreatorId = model.FxUserId,
                    FxUserId = fxUserId,
                    BusinessType = BusinessTypes.ListingTask.ToString(),
                    SubBusinessType = SubBusinessTypes.ProcessListingTaskBusiness.ToString(),
                    BusinessId = code,
                    //Content = model.ToJson(),
                    DbName = "",
                    Remark = "",
            } };
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            #endregion

            //状态更新为进行中
            model.BusinessStatus = 1;
            model.ErrorMessage = "";
            model.ProcessBusinessTime = DateTime.Now;
            UpdateBusinessStatus(model);

            //处理
            try
            {
                result = ProcessListingTaskBusiness(model);
            }
            catch (Exception e)
            {
                result.Success = false;
                Log.WriteError($"ProcessListingTaskBusiness处理时异常，异常原因：{e.Message}，ListingTaskCode={model.ListingTaskCode}，堆栈信息：{e.StackTrace}", $"ProcessListingTaskBusiness-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }

            if (result.Success)
            {
                model.BusinessStatus = 9;
                UpdateBusinessStatus(model);

                //记录业务处理结果
                if (result.Data != null)
                    UpdateExtBusinessProcessResult(model.ListingTaskCode, result.Data.ToJson());
            }
            else
            {
                //更新铺货任务
                model.BusinessStatus = -1;
                model.BusinessRetryCount += 1;
                model.ErrorMessage = result.Message;
                model.NextProcessBusinessTime = DateTime.Now.AddMinutes(Math.Pow(model.BusinessRetryCount, 2));//1,4,9,16
                UpdateBusinessStatus(model);

                //记录业务处理结果
                if (result.Data != null)
                    UpdateExtBusinessProcessResult(model.ListingTaskCode, result.Data.ToJson());
            }

            #region 业务日志2，处理完成后的数据
            var businessLogs2 = new List<BusinessLogModel> { new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = "",
                    SourceType = "铺货任务-处理相关业务逻辑-完成",
                    PlatformType = model.TargetPlatformType,
                    CreatorId = model.FxUserId,
                    FxUserId = fxUserId,
                    BusinessType = BusinessTypes.ListingTask.ToString(),
                    SubBusinessType = SubBusinessTypes.ProcessListingTaskBusiness.ToString(),
                    BusinessId = code,
                    Content = result.ToJson(),
                    DbName = "",
                    //Remark = result.ToJson(),
            } };
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
            #endregion

            return result;
        }

        /// <summary>
        /// 处理业务逻辑
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ReturnedModel<List<ListingTaskBusinessResult>> ProcessListingTaskBusiness(ListingTaskRecords model)
        {
            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var result = new ReturnedModel<List<ListingTaskBusinessResult>>();
            result.Success = false;

            #region 1.前置校验
            if (model == null)
            {
                result.Success = false;
                result.Message = "查无相关数据";
                return result;
            }
            if (model.ProductId.IsNullOrEmpty())
            {
                result.Success = false;
                result.Message = "店铺商品Id为空";
                return result;
            }
            #endregion

            #region 2.获取店铺商品数据（若跨云用WebApi）
            var apiModel = new GetProductFromApiModel
            {
                PlatformType = model.TargetPlatformType,
                ProductId = model.ProductId,
                ShopId = model.TargetShopId,
                FxUserId = model.FxUserId
            };
            var product = GetProductFromApi(apiModel);

            var productCloudPlatform = CustomerConfig.GetCloudPlatformType(model.TargetPlatformType);

            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}-获取店铺商品数据结果={product?.ToJson()}", logFileName);

            var businessResults = new List<ListingTaskBusinessResult>();
            result.Data = businessResults;

            var abnormalModel = new ListingTaskBusinessAbnormal
            {
                ListingTaskCode = model.ListingTaskCode,
                BatchNo = model.BatchNo,
                PtProductUniqueCode = model.PtProductUniqueCode,
                FxUserId = model.FxUserId,
                ShopId = model.TargetShopId,
                ProductCloudPlatform = productCloudPlatform,
                ProductDbName = "",
            };

            if (product == null || product.Skus == null)
            {
                //记录异常
                abnormalModel.BusinessType = ListingTaskBusinessAbnormalType.SyncShopProduct;
                abnormalModel.ErrorMessage = "获取商品数据失败";
                _businessAbnormalService.BatchAdd(new List<ListingTaskBusinessAbnormal> { abnormalModel });

                //业务日志
                businessResults.Add(new ListingTaskBusinessResult
                {
                    SortIndex = 1,
                    BusinessType = ListingTaskBusinessAbnormalType.SyncShopProduct,
                    Status = -1,
                    Message = "获取商品数据失败",
                });

                result.Success = false;
                result.Message = "查无商品数据";
                return result;
            }
            if (product.SourceUserId != model.FxUserId)
            {
                //业务日志
                businessResults.Add(new ListingTaskBusinessResult
                {
                    SortIndex = 1,
                    BusinessType = ListingTaskBusinessAbnormalType.SyncShopProduct,
                    Status = -1,
                    Message = "商品不属于此用户",
                });

                result.Success = false;
                result.Message = "商品不属于此用户";
                return result;
            }

            //业务日志
            businessResults.Add(new ListingTaskBusinessResult
            {
                SortIndex = 1,
                BusinessType = ListingTaskBusinessAbnormalType.SyncShopProduct,
                Status = 1,
                Message = $"PlatformId={product.PlatformId},ProductCode={product.ProductCode},Skus.CargoNumber={product.Skus?.Select(a => a.CargoNumber).ToJson()}",
            });
            #endregion

            #region 3.获取平台资料草稿
            var ptProductInfo = _ptProductInfoService.GetByCode(model.PtProductUniqueCode, model.FxUserId);
            if (ptProductInfo == null)
            {
                //业务日志
                businessResults.Add(new ListingTaskBusinessResult
                {
                    SortIndex = 2,
                    BusinessType = ListingTaskBusinessAbnormalType.QueryPtProductInfo,
                    Status = -1,
                    Message = $"查无平台草稿资料,PtProductUniqueCode={model.PtProductUniqueCode}",
                });

                result.Success = false;
                result.Message = "查无平台草稿资料";
                return result;
            }
            model.PtProductInfo = ptProductInfo;

            var ptProductInfoSkus = new List<PtProductInfoSkuModel>();
            if (ptProductInfo.Ext != null && string.IsNullOrEmpty(ptProductInfo.Ext.SkuJson) == false)
            {
                ptProductInfoSkus = ptProductInfo.Ext.SkuJson.ToObject<List<PtProductInfoSkuModel>>();
            }

            businessResults.Add(new ListingTaskBusinessResult
            {
                SortIndex = 2,
                BusinessType = ListingTaskBusinessAbnormalType.QueryPtProductInfo,
                Status = 1,
                Message = $"查到平台草稿资料,UniqueCode={ptProductInfo?.UniqueCode}",
            });

            #endregion

            #region 4.查询/创建基础商品（含供货厂家设置）
            var isFromSupplierProduct = ptProductInfo.CreateFrom == "SupplierProduct" || ptProductInfo.CreateFrom == "SupplierPtProduct";
            var isExistBaseProduct = true; //是否生成过基础商品
            var isExistPtProductInfo = false;//是否存在平台资料
            var selfBaseProductSkuSupplierConfigs = new List<BaseProductSkuSupplierConfig>();//自己基础商品的供货关系
            BaseProductEntity entity = null;
            if (isFromSupplierProduct)
            {
                isExistBaseProduct = false;

                var fromBaseProductUid = ptProductInfo.FromBaseProductUid;
                var fromSupplierProductUid = ptProductInfo.FromSupplierProductUid;
                if (fromBaseProductUid == 0 && fromSupplierProductUid.HasValue && fromSupplierProductUid.Value > 0)
                {
                    //通过货盘Uid查出基础商品Uid
                    var fromSupplierProduct = new SupplierProductRepository().GetByUid(fromSupplierProductUid.Value);
                    if (fromSupplierProduct != null && fromSupplierProduct.FxUserId == model.FromFxUserId)
                    {
                        fromBaseProductUid = fromSupplierProduct.FromProductUid;
                        ptProductInfo.FromBaseProductUid = fromBaseProductUid;
                    }
                }
                if (fromBaseProductUid <= 0)
                {
                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 3,
                        BusinessType = ListingTaskBusinessAbnormalType.QueryBaseProduct,
                        Status = -1,
                        Message = $"来源基础商品Uid异常,BaseProductUid={ptProductInfo.BaseProductUid}，isFromSupplierProduct={isFromSupplierProduct}",
                    });

                    result.Success = false;
                    result.Message = "来源基础商品Uid异常";
                    return result;
                }

                //（厂家货盘Uid）是否生成过基础商品
                if (fromSupplierProductUid > 0)
                    entity = _baseProductService.GetExistByFromProductUid(fromSupplierProductUid.ToString(), ptProductInfo.FxUserId);

                //拿已存在的基础商品
                if (entity != null)
                {
                    isExistBaseProduct = true;

                    entity.SourceSpuCode = entity.SpuCode;
                    entity.Skus = _baseProductSkuService.GetListByProductUid(entity.Uid, ptProductInfo.FxUserId);
                    entity.Skus?.ForEach(sku => { sku.SourceSkuCode = sku.SkuCode; });

                    //已有的供货关系
                    selfBaseProductSkuSupplierConfigs = _baseProductSkuSupplierConfigService.GetAllListByProductUids(new List<long> { entity.Uid });

                    //是否存在平台资料
                    isExistPtProductInfo = _ptProductInfoService.IsExistByUid(entity.Uid, ptProductInfo.FxUserId, ptProductInfo.PlatformType, 0);

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 3,
                        BusinessType = ListingTaskBusinessAbnormalType.QueryBaseProduct,
                        Status = 1,
                        Message = $"查到已存在的基础商品,Uid={entity.Uid}，isExistPtProductInfo={isExistPtProductInfo}，已有的供货厂家配置={selfBaseProductSkuSupplierConfigs?.Select(a => new { a.RefType, a.RefUid, a.SupplierFxUserId }).ToJson()}",
                    });
                }
                else
                {
                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 3,
                        BusinessType = ListingTaskBusinessAbnormalType.QueryBaseProduct,
                        Status = 0,
                        Message = $"未生成过基础商品,fromSupplierProductUid={fromSupplierProductUid}，isFromSupplierProduct={isFromSupplierProduct}",
                    });
                }
            }
            else
            {
                entity = _baseProductService.GetByUid(ptProductInfo.BaseProductUid, ptProductInfo.FxUserId);
                if (entity == null)
                {
                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 3,
                        BusinessType = ListingTaskBusinessAbnormalType.QueryBaseProduct,
                        Status = -1,
                        Message = $"查无自己的基础商品数据,BaseProductUid={ptProductInfo.BaseProductUid}，isFromSupplierProduct={isFromSupplierProduct}",
                    });

                    result.Success = false;
                    result.Message = "查无基础商品数据";
                    return result;
                }
                entity.SourceSpuCode = entity.SpuCode;
                entity.Skus = _baseProductSkuService.GetListByProductUid(ptProductInfo.BaseProductUid, ptProductInfo.FxUserId);
                entity.Skus?.ForEach(sku => { sku.SourceSkuCode = sku.SkuCode; });

                //是否存在平台资料
                isExistPtProductInfo = _ptProductInfoService.IsExistByUid(entity.Uid, ptProductInfo.FxUserId, ptProductInfo.PlatformType, 0);

                //已有的供货关系
                selfBaseProductSkuSupplierConfigs = _baseProductSkuSupplierConfigService.GetAllListByProductUids(new List<long> { entity.Uid });

                //业务日志
                businessResults.Add(new ListingTaskBusinessResult
                {
                    SortIndex = 3,
                    BusinessType = ListingTaskBusinessAbnormalType.QueryBaseProduct,
                    Status = 1,
                    Message = $"查到自己的基础商品,Uid={entity?.Uid}，基础商品规格数量={entity.Skus?.Count()}，isExistPtProductInfo={isExistPtProductInfo}，已有的供货厂家配置={selfBaseProductSkuSupplierConfigs?.Select(a => new { a.RefType, a.RefUid, a.SupplierFxUserId }).ToJson()}",
                });
            }

            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}：isFromSupplierProduct={isFromSupplierProduct}，isExistBaseProduct={isExistBaseProduct}，isExistPtProductInfo={isExistPtProductInfo}，selfBaseProductSkuSupplierConfigs={selfBaseProductSkuSupplierConfigs?.ToJson()}", logFileName);

            if (isExistBaseProduct == false)
            {
                try
                {
                    //创建基础商品+平台资料
                    entity = CreateBaseProductFromListingTask(model);

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 4,
                        BusinessType = ListingTaskBusinessAbnormalType.CreateBaseProduct,
                        Status = 1,
                        Message = $"创建基础商品+平台资料,创建结果Uid={entity?.Uid}，基础商品规格数量={entity?.Skus?.Count()}",
                    });
                }
                catch (Exception ex)
                {
                    //记录异常
                    abnormalModel.BusinessType = ListingTaskBusinessAbnormalType.CreateBaseProduct;
                    abnormalModel.ErrorMessage = ex.Message;
                    _businessAbnormalService.BatchAdd(new List<ListingTaskBusinessAbnormal> { abnormalModel });

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 4,
                        BusinessType = ListingTaskBusinessAbnormalType.CreateBaseProduct,
                        Status = -1,
                        Message = $"创建基础商品+平台资料时异常,ex={ex}",
                    });
                }

                if (entity == null)
                {
                    result.Success = false;
                    result.Message = "创建基础商品异常";
                    return result;
                }
            }
            else
            {
                if (isExistPtProductInfo == false)
                {
                    //创建平台资料
                    var ptProduct = CreatePtProductInfoFromListingTask(entity, model);

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 5,
                        BusinessType = ListingTaskBusinessAbnormalType.CreatePtProductInfo,
                        Status = 1,
                        Message = $"创建平台资料成功，UniqueCode={ptProduct.UniqueCode}",
                    });
                }
            }

            //是否要处理基础商品的绑定关系
            var processBPSupplierConfig = false;
            if (selfBaseProductSkuSupplierConfigs != null && selfBaseProductSkuSupplierConfigs.Any())
            {
                processBPSupplierConfig = true;
            }

            #endregion

            #region 5.溯源处理

            //Dictionary<用户Id,关联关系数据>
            var dicRelations = new Dictionary<int, List<BaseOfPtSkuRelation>>();

            //Dictionary<基础规格Uid,源SkuCode>
            var dicSkus = new Dictionary<long, string>();

            //Dictionary<来源用户Id,基础商品Uid>
            var dicFromUid = new Dictionary<int, long>();

            //路径流节点的用户，从自己-->中间商-->厂家往上溯源
            //Dictionary<店铺规格SkuCode,各节点用户>
            var dicSkuNoteFxUserId = new Dictionary<string, List<int>>();

            //店铺规格与基础规格的关系
            product.Skus?.ForEach(sku =>
            {
                BaseProductSku exist = null;
                PtProductInfoSkuModel ptSku = null;
                if (string.IsNullOrEmpty(sku.OuterSkuId) == false)
                {
                    exist = entity.Skus.FirstOrDefault(a => a.SourceSkuCode == sku.OuterSkuId);
                    ptSku = ptProductInfoSkus.FirstOrDefault(a => a.SkuCode == sku.OuterSkuId);
                }
                if (exist == null && string.IsNullOrEmpty(sku.CargoNumber) == false)
                {
                    exist = entity.Skus.FirstOrDefault(a => a.SourceSkuCode == sku.CargoNumber);
                }
                if (ptSku == null && string.IsNullOrEmpty(sku.CargoNumber) == false)
                {
                    ptSku = ptProductInfoSkus.FirstOrDefault(a => a.SkuCode == sku.CargoNumber);
                }

                if (exist != null && dicSkus.ContainsKey(exist.Uid) == false)
                {
                    dicSkus.Add(exist.Uid, exist.SourceSkuCode);

                    var settlePrice = exist.SettlePrice ?? 0;
                    if (ptSku != null)
                        settlePrice = ptSku.DistributePrice;//草稿资料里的采购价

                    var relation = new BaseOfPtSkuRelation
                    {
                        BaseProductSkuUid = exist.Uid,
                        BaseProductUid = exist.BaseProductUid,
                        ProductSkuPtId = sku.SkuId,
                        ProductSkuCode = sku.SkuCode,
                        ProductPtId = product.PlatformId,
                        ProductCode = product.ProductCode,
                        ProductShopId = product.ShopId,
                        ProductFxUserId = product.SourceUserId,
                        ProductPlatformType = product.PlatformType,
                        FxUserId = model.FxUserId,
                        CreateTime = DateTime.Now,
                        UpdateTime = DateTime.Now,
                        Status = 1,
                        RelationType = 1,
                        CloudPlatform = productCloudPlatform,
                        SettlePrice = settlePrice    //采购价
                    };

                    if (dicRelations.ContainsKey(model.FxUserId) == false)
                    {
                        dicRelations.Add(model.FxUserId, new List<BaseOfPtSkuRelation>() { relation });
                    }
                    else
                    {
                        dicRelations[model.FxUserId].Add(relation);
                    }

                    if (dicSkuNoteFxUserId.ContainsKey(sku.SkuCode) == false)
                    {
                        dicSkuNoteFxUserId.Add(sku.SkuCode, new List<int> { product.SourceUserId });
                    }
                }
            });

            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，product.Skus.CargoNumber={product.Skus?.Select(a => a.CargoNumber).ToJson()}，entity.Skus.SourceSkuCode={entity.Skus?.Select(a => a.SourceSkuCode).ToJson()}，规格匹配结果，dicSkus={dicSkus.ToJson()}", logFileName);

            var needTraceSourceSkus = entity.Skus.Where(a => a.UpFxUserId != 0 && a.UpFxUserId != a.FxUserId && a.UpSkuUid.ToLong() > 0).ToList();

            if (needTraceSourceSkus.Any())
            {
                needTraceSourceSkus.GroupBy(g => g.UpFxUserId).ToList().ForEach(g =>
                {
                    //按用户维度递归溯源
                    var traceModel = new TraceToSourceModel
                    {
                        Product = product,
                        ProductCloudPlatform = productCloudPlatform,
                        UpFxUserId = g.Key,
                        AgentSkus = g.ToList(),
                        DicSkus = dicSkus,
                        DicRelations = dicRelations,
                        DicSkuNoteFxUserId = dicSkuNoteFxUserId,
                        Level = 0
                    };

                    TraceToSource(traceModel);
                });
            }


            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，处理供货关系前，dicSkuNoteFxUserId={dicSkuNoteFxUserId.ToJson()}", logFileName);

            //有基础商品供货厂家的配置

            if (processBPSupplierConfig)
            {
                //使用供货厂家匹配的结果
                var dicSkuNoteFxUserIdFromConfig = new Dictionary<string, List<int>>();

                //Sku维度
                var skuConfigs = selfBaseProductSkuSupplierConfigs.Where(a => a.RefType != "Product").ToList();
                if (skuConfigs.Any() && dicRelations.ContainsKey(model.FxUserId))
                {
                    var selfRelations = dicRelations[model.FxUserId].ToList();
                    skuConfigs.ToList().ForEach(s =>
                    {
                        var curRelations = selfRelations.Where(a => a.BaseProductSkuUid == s.RefUid).ToList();
                        foreach (var rel in curRelations)
                        {
                            if (dicSkuNoteFxUserIdFromConfig.ContainsKey(rel.ProductSkuCode))
                            {
                                dicSkuNoteFxUserIdFromConfig[rel.ProductSkuCode].Add(s.SupplierFxUserId);
                            }
                            else
                            {
                                //第一节点为自己
                                dicSkuNoteFxUserIdFromConfig.Add(rel.ProductSkuCode, new List<int> { model.FxUserId });

                                //第二节点为厂家
                                if (model.FxUserId != s.SupplierFxUserId)
                                    dicSkuNoteFxUserIdFromConfig[rel.ProductSkuCode].Add(s.SupplierFxUserId);
                            }
                        }
                    });

                    //商品维度
                    var spuConfig = selfBaseProductSkuSupplierConfigs.FirstOrDefault(a => a.RefType == "Product");
                    if (spuConfig != null && spuConfig.SupplierFxUserId != model.FxUserId && dicSkuNoteFxUserId.ContainsKey(product.ProductCode) == false)
                    {
                        //第一节点为自己
                        dicSkuNoteFxUserId.Add(product.ProductCode, new List<int> { model.FxUserId });

                        //第二节点为厂家
                        dicSkuNoteFxUserId[product.ProductCode].Add(spuConfig.SupplierFxUserId);
                    }

                    //针对只有一级节点的，使用供货厂家配置匹配的结果
                    dicSkuNoteFxUserId.ToList().ForEach(g =>
                    {
                        if (g.Value.Count == 1 && dicSkuNoteFxUserIdFromConfig.ContainsKey(g.Key))
                        {
                            dicSkuNoteFxUserId[g.Key] = dicSkuNoteFxUserIdFromConfig[g.Key];
                        }
                    });
                }
            }

            var dicPathFlow = new Dictionary<string, PathFlow>();
            var dicPathFlowReference = new Dictionary<string, PathFlowReference>();

            dicSkuNoteFxUserId.ToList().ForEach(g =>
            {
                var relationCode = Guid.NewGuid().ToString().ToShortMd5();
                var configs = new List<PathFlowReferenceConfig>();
                var pathFlow = new PathFlow
                {
                    SourceFxUserId = product.SourceUserId,
                    SourceShopId = product.ShopId,
                    CreateBy = product.SourceUserId,
                    CreateTime = DateTime.Now,
                    PathFlowNodes = new List<PathFlowNode>()
                };
                var nodeIndex = 0;
                var upFxUserId = 0;
                var downFxUserId = 0;
                var arrFxUserId = g.Value.ToArray();

                //按NodeFxUserId顺序组装成PathFlow+List<PathFlowNode>
                g.Value.ForEach(fxUserId =>
                {
                    if (nodeIndex == 0)
                    {
                        upFxUserId = 0;
                    }
                    else
                    {
                        configs.Add(new PathFlowReferenceConfig
                        {
                            RelationCode = relationCode,
                            ParentUniqueKey = relationCode,
                            FxUserId = fxUserId,
                            CreateTime = DateTime.Now
                        });
                    }
                    if (nodeIndex == arrFxUserId.Length - 1)
                    {
                        downFxUserId = 0;
                    }
                    else if (nodeIndex < arrFxUserId.Length - 1)
                    {
                        downFxUserId = arrFxUserId[nodeIndex + 1];
                    }
                    pathFlow.PathFlowNodes.Add(new PathFlowNode
                    {
                        UpFxUserId = upFxUserId,
                        FxUserId = fxUserId,
                        DownFxUserId = downFxUserId,
                    });

                    upFxUserId = fxUserId;
                    nodeIndex += 1;
                });
                var pathFlowCode = PathFlowService.CreatePathFlowCode(pathFlow);
                pathFlow.PathFlowCode = pathFlowCode;
                pathFlow.PathFlowNodes.ForEach(x =>
                {
                    x.PathFlowCode = pathFlowCode;
                });
                configs.ForEach(c => { c.PathFlowCode = pathFlowCode; });

                if (dicPathFlow.ContainsKey(pathFlowCode) == false)
                {
                    dicPathFlow.Add(pathFlowCode, pathFlow);
                }
                var pfr = new PathFlowReference
                {
                    PathFlowCode = pathFlowCode,
                    PathFlowRefCode = g.Key,
                    PathFlowRefType = "Sku",
                    ProductCode = product.ProductCode,
                    RelationCode = relationCode,
                    UniqueKey = relationCode,
                    ReferenceConfigs = configs,
                    CreateTime = DateTime.Now
                };
                dicPathFlowReference.Add(g.Key, pfr);
            });


            //业务日志
            businessResults.Add(new ListingTaskBusinessResult
            {
                SortIndex = 6,
                BusinessType = ListingTaskBusinessAbnormalType.TraceToSource,
                Status = 1,
                Message = $"溯源结果：dicPathFlow={dicPathFlow.ToJson()}，dicPathFlowReference={dicPathFlowReference.Select(a => new { a.Value.ProductCode, a.Value.RelationCode, a.Value.PathFlowRefType, a.Value.PathFlowRefCode }).ToJson()}，dicRelations={dicRelations.ToJson()}，dicSkus={dicSkus.ToJson()}",
            });

            //带有商品维度关系的数量
            int productCount = 0;
            if (dicPathFlowReference.ContainsKey(product.ProductCode))
            {
                dicPathFlowReference[product.ProductCode].PathFlowRefType = "Product";
                productCount = 1;
            }

            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，dicSkuNoteFxUserId={dicSkuNoteFxUserId.ToJson()}，溯源结果：dicPathFlow={dicPathFlow.ToJson()}，dicPathFlowReference={dicPathFlowReference.ToJson()}，dicRelations={dicRelations.ToJson()}，dicSkus={dicSkus.ToJson()}", logFileName);


            //所有规格的路径流相同-->转为商品维度，只需要一条记录
            if (dicPathFlowReference != null && dicPathFlowReference.Any() && dicPathFlowReference.Count() - productCount == product.Skus.Count() && dicPathFlowReference.Values.GroupBy(g => g.PathFlowCode).Count() == 1)
            {
                var newPfr = dicPathFlowReference.First().Value.ToJson().ToObject<PathFlowReference>();
                newPfr.PathFlowRefType = "Product";
                newPfr.ReferenceConfigs = new List<PathFlowReferenceConfig>();
                newPfr.PathFlowRefCode = newPfr.ProductCode;

                dicPathFlowReference = new Dictionary<string, PathFlowReference>();
                dicPathFlowReference.Add(newPfr.ProductCode, newPfr);

                Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，转为商品维度，dicPathFlowReference={dicPathFlowReference.ToJson()}", logFileName);

            }

            #endregion

            #region 6.关联同款
            var productSettlementPrices = new List<ProductSettlementPrice>();
            if (dicRelations != null && dicRelations.Any())
            {
                //同步采购价到业务库
                if (dicRelations.ContainsKey(model.FxUserId) && model.FxUserId != model.FromFxUserId)
                {
                    var selfRelations = dicRelations[model.FxUserId];
                    selfRelations.Where(r => r.SettlePrice > 0).ToList().ForEach(rel =>
                    {
                        //规格维度生成结算价Model
                        var curSettPrice = new ProductSettlementPrice
                        {
                            ProductCode = rel.ProductCode,
                            ProductSkuCode = rel.ProductSkuCode,
                            CreateUser = model.FxUserId,
                            SettlementType = SettlementType.Merchant.ToInt(),
                            Price = rel.SettlePrice,
                            PlatformType = rel.ProductPlatformType,
                            FxUserId = model.FromFxUserId,   //厂家Id
                            CreateTime = DateTime.Now
                        };
                        productSettlementPrices.Add(curSettPrice);
                    });
                }

                //按用户维度处理
                dicRelations.ToList().ForEach(g =>
                {
                    var _service = new BaseOfPtSkuRelationService(g.Key);
                    try
                    {

                        Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，关联同款，当前执行的用户=g.Key={g.Key}，所在库={_service.baseRepository?.DbConnection?.Database}，g.Value={g.Value.ToJson()}", logFileName);

                        //基础商品库
                        _service.Merger(g.Value);

                        //业务库（WebApi）
                        var apiUrl = "/BaseProductApi/AddBaseOfPtSkuRelation";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(productCloudPlatform);
                        if (string.IsNullOrEmpty(targetSiteUrl) == false)
                        {
                            targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，WebApi，targetSiteUrl={targetSiteUrl}，apiModel={apiModel.ToJson()}", logFileName);

                            //以g.Key的用户调用
                            var postResult = WebCommon.PostFxSiteApi<List<BaseOfPtSkuRelation>, bool>(targetSiteUrl, g.Key, g.Value, "添加业务库关联同款", isEncrypt: true);
                        }

                        //业务日志
                        businessResults.Add(new ListingTaskBusinessResult
                        {
                            SortIndex = 7,
                            BusinessType = ListingTaskBusinessAbnormalType.BaseOfPtSkuRelation,
                            Status = 1,
                            Message = $"关联同款,g.key={g.Key},g.Value={g.Value.ToJson()}",
                        });

                    }
                    catch (Exception ex)
                    {
                        //记录异常
                        abnormalModel.BusinessType = ListingTaskBusinessAbnormalType.BaseOfPtSkuRelation;
                        abnormalModel.ErrorMessage = ex.Message;
                        _businessAbnormalService.BatchAdd(new List<ListingTaskBusinessAbnormal> { abnormalModel });

                        //业务日志
                        businessResults.Add(new ListingTaskBusinessResult
                        {
                            SortIndex = 7,
                            BusinessType = ListingTaskBusinessAbnormalType.BaseOfPtSkuRelation,
                            Status = -1,
                            Message = $"关联同款时异常,g.key={g.Key},ex={ex}",
                        });
                    }

                });
            }
            #endregion

            #region 7.店铺商品处理绑定厂家（若跨云用WebApi）

            if (dicPathFlow != null && dicPathFlow.Any())
            {
                try
                {
                    if (CustomerConfig.CloudPlatformType == productCloudPlatform)
                    {
                        //同云
                        new PathFlowService().BulkMerger(dicPathFlow.Values.ToList());
                        new PathFlowReferenceService().BulkMergerFromListing(dicPathFlowReference.Values.ToList(), new List<int> { model.FxUserId });
                    }
                    else
                    {
                        //跨云
                        //业务库（WebApi）
                        var apiUrl = "/BaseProductApi/BindSupplierForListing";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(productCloudPlatform);
                        if (string.IsNullOrEmpty(targetSiteUrl) == false)
                        {
                            targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                            var curApiModel = new BindSupplierForListingModel
                            {
                                PathFlows = dicPathFlow.Values.ToList(),
                                PathFlowReferences = dicPathFlowReference.Values.ToList(),
                                FxUserId = model.FxUserId
                            };

                            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，WebApi，targetSiteUrl={targetSiteUrl}，curApiModel={curApiModel.ToJson()}", logFileName);

                            var postResult = WebCommon.PostFxSiteApi<BindSupplierForListingModel, bool>(targetSiteUrl, product.SourceUserId, curApiModel, "跨云处理路径关系-铺货", isEncrypt: true);
                        }
                    }

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 9,
                        BusinessType = ListingTaskBusinessAbnormalType.ProductBindSupplier,
                        Status = 1,
                        Message = $"店铺商品处理绑定厂家，productCloudPlatform={productCloudPlatform}",
                    });
                }
                catch (Exception ex)
                {
                    //记录异常
                    abnormalModel.BusinessType = ListingTaskBusinessAbnormalType.ProductBindSupplier;
                    abnormalModel.ErrorMessage = ex.Message;
                    _businessAbnormalService.BatchAdd(new List<ListingTaskBusinessAbnormal> { abnormalModel });

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 9,
                        BusinessType = ListingTaskBusinessAbnormalType.ProductBindSupplier,
                        Status = -1,
                        Message = $"店铺商品处理绑定厂家时异常,ex={ex}",
                    });
                }
            }
            #endregion

            #region 8.同步采购价（若跨云用WebApi）
            if (productSettlementPrices.Any())
            {
                try
                {
                    if (CustomerConfig.CloudPlatformType == productCloudPlatform)
                    {
                        //同云
                        var changeType = ChangeType.SyncBaseProduct.ToInt();
                        new FinancialSettlementService().SetProductSettlementPrice(productSettlementPrices, model.FxUserId, changeType: changeType, needSetIdFromDb: true);
                    }
                    else
                    {
                        //跨云
                        //业务库（WebApi）
                        var apiUrl = "/BaseProductApi/SetProductSettlementPrice";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(productCloudPlatform);
                        if (string.IsNullOrEmpty(targetSiteUrl) == false)
                        {
                            targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                            Log.Debug($"ProcessListingTaskBusiness-{model.ListingTaskCode}，WebApi，targetSiteUrl={targetSiteUrl}，productSettlementPrices={productSettlementPrices.ToJson()}", logFileName);

                            var postResult = WebCommon.PostFxSiteApi<List<ProductSettlementPrice>, bool>(targetSiteUrl, product.SourceUserId, productSettlementPrices, "跨云处同步采购价-铺货", isEncrypt: true);
                        }
                    }

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 10,
                        BusinessType = ListingTaskBusinessAbnormalType.SyncSettlementPrice,
                        Status = 1,
                        Message = $"同步采购价，productSettlementPrices={productSettlementPrices.Select(a => new { a.ProductSkuCode, a.CreateUser, a.FxUserId, a.Price }).ToJson()}",
                    });
                }
                catch (Exception ex)
                {
                    //记录异常
                    abnormalModel.BusinessType = ListingTaskBusinessAbnormalType.SyncSettlementPrice;
                    abnormalModel.ErrorMessage = ex.Message;
                    _businessAbnormalService.BatchAdd(new List<ListingTaskBusinessAbnormal> { abnormalModel });

                    //业务日志
                    businessResults.Add(new ListingTaskBusinessResult
                    {
                        SortIndex = 10,
                        BusinessType = ListingTaskBusinessAbnormalType.ProductBindSupplier,
                        Status = -1,
                        Message = $"同步采购价时异常,ex={ex}",
                    });
                }
            }
            #endregion

            #region 9.若是重新铺货，触发同步店铺类目（只发MQ）-- 移除
            /*
            if (model.IsResetListing)
            {
                //发送MQ
                var message = new ShopAuthMessageModel
                {
                    ShopId = product.ShopId,
                    PlatformType = model.TargetPlatformType,
                    Time = DateTime.Now,
                };
                if (CustomerConfig.IsDebug == false)
                    RabbitMQService.SendMessage(message, RabbitMQService.FxShopAuthMessageDescription);

                //业务日志
                businessResults.Add(new ListingTaskBusinessResult
                {
                    SortIndex = 11,
                    BusinessType = ListingTaskBusinessAbnormalType.ResetListingSendMQ,
                    Status = 1,
                    Message = $"重新铺货，触发同步店铺类目（只发MQ）",
                });

            }
            */
            #endregion

            result.Data = businessResults;
            result.Success = true;
            return result;
        }

        /// <summary>
        /// 递归溯源
        /// </summary>
        /// <param name="traceModel"></param>
        public void TraceToSource(TraceToSourceModel traceModel)
        {
            if (traceModel.Level >= 20)
                return;

            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            traceModel.Level += 1;
            var upFxUserId = traceModel.UpFxUserId;
            var product = traceModel.Product;

            //查询上级厂家数据
            var dbConfigModel = new ProductDbConfigRepository().GetDbConfigModel(upFxUserId);
            var upConnectionString = dbConfigModel?.ConnectionString;
            var isUseMySQL = dbConfigModel?.IsMySQL ?? true;
            var upSkuService = new BaseProductSkuService(upConnectionString, isUseMySQL);
            var upConfigService = new BaseProductSkuSupplierConfigService(upConnectionString, isUseMySQL);

            var upSkuUids = traceModel.AgentSkus.Select(a => a.UpSkuUid.ToLong()).Distinct().ToList();
            var upSkus = upSkuService.GetListBySkuUids(upSkuUids, upFxUserId);
            if (upSkus == null || upSkus.Any() == false)
                return;

            //规格对应关系，关联同款
            upSkus.ForEach(upSku =>
            {
                var existAgentSku = traceModel.AgentSkus.FirstOrDefault(a => a.UpSkuUid == upSku.Uid.ToString());

                Log.Debug($"1结果.traceModel.DicSkuNoteFxUserId={traceModel.DicSkuNoteFxUserId.ToJson()}", logFileName);

                if (existAgentSku == null || traceModel.DicSkus.ContainsKey(existAgentSku.Uid) == false)
                    return;

                var cargoNumber = traceModel.DicSkus[existAgentSku.Uid];//店铺规格用户自定义的编码
                if (traceModel.DicSkus.ContainsKey(upSku.Uid) == false)
                {
                    traceModel.DicSkus.Add(upSku.Uid, traceModel.DicSkus[existAgentSku.Uid]);

                    var existShopSku = traceModel.Product.Skus.FirstOrDefault(a => a.CargoNumber == cargoNumber);
                    if (existShopSku != null)
                    {
                        var relation = new BaseOfPtSkuRelation
                        {
                            BaseProductSkuUid = upSku.Uid,
                            BaseProductUid = upSku.BaseProductUid,
                            ProductSkuPtId = existShopSku.SkuId,
                            ProductSkuCode = existShopSku.SkuCode,
                            ProductPtId = product.PlatformId,
                            ProductCode = product.ProductCode,
                            ProductShopId = product.ShopId,
                            ProductFxUserId = product.SourceUserId,
                            ProductPlatformType = product.PlatformType,
                            FxUserId = upFxUserId,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now,
                            Status = 1,
                            RelationType = 1,
                            CloudPlatform = traceModel.ProductCloudPlatform,

                            SettlePrice = upSku.DistributePrice ?? 0
                        };

                        //当前厂家需要关联同款的数据
                        if (traceModel.DicRelations.ContainsKey(upFxUserId) == false)
                        {
                            traceModel.DicRelations.Add(upFxUserId, new List<BaseOfPtSkuRelation>() { relation });
                        }
                        else
                        {
                            traceModel.DicRelations[upFxUserId].Add(relation);
                        }

                        //追加厂家作为此规格的路径流节点
                        if (traceModel.DicSkuNoteFxUserId.ContainsKey(existShopSku.SkuCode))
                        {
                            traceModel.DicSkuNoteFxUserId[existShopSku.SkuCode].Add(upFxUserId);
                        }
                    }

                }
            });

            Log.Debug($"1结果.traceModel.DicSkuNoteFxUserId={traceModel.DicSkuNoteFxUserId.ToJson()}", logFileName);

            var upUids = upSkus.Select(a => a.BaseProductUid).Distinct().ToList();
            var upConfigs = upConfigService.GetAllListByProductUids(upUids);
            Log.Debug($"2 upConfigs={upConfigs.ToJson()}.traceModel={traceModel.ToJson()}", logFileName);
            if (upConfigs == null || upConfigs.Any() == false)
                return;

            //UpFxUserId与供货厂家相同，才继续溯源
            var needTraceSourceSkus = upSkus.Where(a => a.UpFxUserId != 0 && a.UpFxUserId != a.FxUserId && a.UpSkuUid.ToLong() > 0 && upConfigs.Any(b => b.SupplierFxUserId == a.UpFxUserId && (b.RefUid == a.Uid || b.RefUid == a.BaseProductUid))).ToList();

            Log.Debug($"2 needTraceSourceSkus={needTraceSourceSkus.ToJson()}", logFileName);

            if (needTraceSourceSkus.Any())
            {
                needTraceSourceSkus.GroupBy(g => g.UpFxUserId).ToList().ForEach(g =>
                {
                    //按用户维度递归溯源
                    traceModel.AgentSkus = g.ToList();
                    traceModel.UpFxUserId = g.Key;
                    TraceToSource(traceModel);
                });
            }
        }

        /// <summary>
        /// 获取商品数据（若跨云用WebApi）
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="platformType"></param>
        /// <param name="shopId"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ProductFx GetProductFromApi(GetProductFromApiModel apiModel)
        {
            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            var cloudPt = CustomerConfig.GetCloudPlatformType(apiModel.PlatformType);
            if (cloudPt == CloudPlatformType.Alibaba.ToString())
            {
                // 同云 
                Log.Debug($"同云，apiModel={apiModel.ToJson()}", logFileName);
                return new ProductFxService().GetProductForListing(apiModel);
            }
            else
            {
                // 跨云
                try
                {
                    var apiUrl = "/BaseProductApi/GetProductForListing";
                    var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(cloudPt);
                    //if (CustomerConfig.IsLocalDbDebug)
                    //{
                    //    //临时测试
                    //    targetSiteUrl = "https://6tfxdd.dgjapp.com";
                    //    apiModel.FxUserId = 74;
                    //}
                    if (targetSiteUrl.IsNullOrEmpty())
                        return null;

                    targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;

                    Log.Debug($"跨云，targetSiteUrl={targetSiteUrl}，apiModel={apiModel.ToJson()}", logFileName);

                    return WebCommon.PostFxSiteApi<GetProductFromApiModel, ProductFx>(targetSiteUrl, apiModel.FxUserId, apiModel, "跨云查询商品详情接口", isEncrypt: true);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"跨云查询商品详情接口：{ex.Message}");
                }
            }
            return null;
        }

        /// <summary>
        /// 通过铺货任务创建基础商品+平台资料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public BaseProductEntity CreateBaseProductFromListingTask(ListingTaskRecords model)
        {
            if (model.PtProductInfo == null)
                return null;

            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            Log.Debug($"CreateBaseProductFromListingTask:开始", logFileName);

            var addModel = TransferPtProductInfoToBaseProduct(model.PtProductInfo);
            var entity = _baseProductSkuService.CreateBaseProductSku(addModel, model.FxUserId);
            PtProductInfo ptProductInfo = null;
            //创建平台资料
            if (entity != null)
            {
                ptProductInfo = CreatePtProductInfoFromListingTask(entity, model);
                if (ptProductInfo != null)
                    entity.PtProductInfos = new List<PtProductInfo>() { ptProductInfo };
            }

            Log.Debug($"CreateBaseProductFromListingTask:完成，entity={entity?.ToJson()}", logFileName);

            return entity;
        }

        /// <summary>
        /// 通过铺货任务创建平台资料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PtProductInfo CreatePtProductInfoFromListingTask(BaseProductEntity entity, ListingTaskRecords model)
        {
            if (model.PtProductInfo == null)
                return null;

            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            var ptProductInfo = model.PtProductInfo.ToJson().ToObject<PtProductInfo>();

            var uniqueCode = Guid.NewGuid().ToString().ToShortMd5();
            ptProductInfo.ShopId = 0;
            ptProductInfo.Status = 1;
            ptProductInfo.UniqueCode = uniqueCode;
            ptProductInfo.BaseProductUid = entity.Uid;
            ptProductInfo.SharePathCode = entity.SharePathCode;
            ptProductInfo.PathNodeDeep = entity.PathNodeDeep;
            ptProductInfo.CreateTime = DateTime.Now;
            ptProductInfo.UpdateTime = DateTime.Now;

            if (ptProductInfo.Ext != null && string.IsNullOrEmpty(ptProductInfo.Ext.SkuJson) == false)
            {
                var skus = ptProductInfo.Ext.SkuJson.ToObject<List<PtProductInfoSkuModel>>();
                skus?.ForEach(sku =>
                {
                    var existSku = entity.Skus?.FirstOrDefault(a => a.Uid.ToString2() == sku.FromBaseProductSkuUid);
                    if (existSku == null)
                    {
                        existSku = entity.Skus?.FirstOrDefault(a => a.SkuCode == sku.SkuCode);
                    }
                    if (existSku != null)
                    {
                        sku.SharePathCode = existSku.SharePathCode;
                        sku.PathNodeDeep = existSku.PathNodeDeep;
                        sku.FromBaseProductSkuUid = existSku.Uid.ToString2();
                    }
                });
                ptProductInfo.Ext.PtProductUniqueCode = uniqueCode;
                ptProductInfo.Ext.SkuJson = skus?.ToJson();
            }
            if (ptProductInfo.Ext == null)
                ptProductInfo.Ext = new PtProductInfoExt { PtProductUniqueCode = uniqueCode };

            ptProductInfo.Ext.CreateTime = DateTime.Now;
            ptProductInfo.Ext.UpdateTime = DateTime.Now;

            //创建平台资料，加锁
            var keyId = $"{ptProductInfo.BaseProductUid}/{ptProductInfo.FxUserId}/{ptProductInfo.PlatformType}/0";
            var key = CacheKeys.CreatePtProductInfoLockKey.Replace("{Id}", keyId);
            var isLocked = RedisHelper.Exists(key);
            Log.Debug($"创建平台资料：isLocked={isLocked}，key={key}", logFileName);
            //锁60秒
            if (isLocked == false && RedisHelper.Set(key, DateTime.Now, 60))
            {
                //保存前再次检查：是否存在平台资料
                var isExistPtProductInfo = _ptProductInfoService.IsExistByUid(ptProductInfo.BaseProductUid, ptProductInfo.FxUserId, ptProductInfo.PlatformType, 0);
                if (isExistPtProductInfo == false)
                    _ptProductInfoService.Add(ptProductInfo);
                RedisHelper.Del(key);
                Log.Debug($"创建平台资料：isLocked={isLocked}，isExistPtProductInfo={isExistPtProductInfo}", logFileName);
            }

            return ptProductInfo;
        }

        /// <summary>
        /// 平台资料转基础商品
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public BaseProductSkuAddModel TransferPtProductInfoToBaseProduct(PtProductInfo ptProductInfo)
        {
            if (ptProductInfo == null || ptProductInfo.Ext == null)
                return null;

            var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            Log.Debug($"TransferPtProductInfoToBaseProduct:开始", logFileName);

            var addModel = new BaseProductSkuAddModel();
            var skus = new List<PtProductInfoSkuModel>();

            var isFromSupplier = false;
            var curFxUserId = ptProductInfo.FxUserId;
            var supplierFxUserId = 0;
            if (ptProductInfo.FromFxUserId != curFxUserId)
            {
                isFromSupplier = true;
                supplierFxUserId = ptProductInfo.FromFxUserId;
            }

            var skuCodes = new List<string>();
            if (ptProductInfo.Ext != null && string.IsNullOrEmpty(ptProductInfo.Ext.SkuJson) == false)
            {
                skus = ptProductInfo.Ext.SkuJson.ToObject<List<PtProductInfoSkuModel>>();
                skuCodes = skus.Select(a => a.SkuCode).ToList();
            }

            // 查询用户在基础商品库中已存在的SkuCode
            var existSkuCodes = _baseProductSkuService.GetBaseProductSkuByCode(skuCodes, true, curFxUserId);
            var existSpuCodes = _baseProductSkuService.GetBaseProductSkuByCode(new List<string> { ptProductInfo.SpuCode }, false, curFxUserId);

            //本身重复的SkuCode
            var selfExitSkuCodes = skus.Where(a => string.IsNullOrEmpty(a.SkuCode) == false).GroupBy(g => g.SkuCode).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
            if (selfExitSkuCodes != null && selfExitSkuCodes.Any())
            {
                existSkuCodes.AddRange(selfExitSkuCodes);
            }

            //共享路径流
            var sharePathCodes = new List<string>();
            if (string.IsNullOrEmpty(ptProductInfo.SharePathCode) == false)
                sharePathCodes.Add(ptProductInfo.SharePathCode);
            sharePathCodes.AddRange(skus.Where(a => string.IsNullOrEmpty(a.SharePathCode) == false).Select(a => a.SharePathCode).Distinct());
            var sharePathEntList = _sharePathFlowRepository.GetListByCodes(sharePathCodes);
            var sharePathNodeEntList = _sharePathFlowNodeRepository.GetBySharePathFlowCodes(sharePathCodes);

            addModel.CreateFrom = "Listing";
            if (string.IsNullOrEmpty(ptProductInfo.Ext.Description) == false)
                addModel.DescriptionStr = ptProductInfo.Ext.Description.Split(',').ToList();
            addModel.FromFxUserId = ptProductInfo.FromFxUserId;
            addModel.FromProductUid = ptProductInfo.FromSupplierProductUid.ToString();//货盘商品Uid
            addModel.SourceSpuCode = ptProductInfo.SpuCode;
            //如果存在则随机生成
            addModel.SpuCode = existSpuCodes.Contains(ptProductInfo.SpuCode) ? Guid.NewGuid().ToString().ToShortMd5() : ptProductInfo.SpuCode;
            addModel.Subject = ptProductInfo.Subject;
            addModel.RootNodeFxUserId = ptProductInfo.RootNodeFxUserId;
            addModel.SharePathCode = ptProductInfo.SharePathCode;
            addModel.PathNodeDeep = ptProductInfo.PathNodeDeep;
            if (isFromSupplier)
                addModel.PathNodeDeep = ptProductInfo.PathNodeDeep + 1;
            if (isFromSupplier && string.IsNullOrEmpty(ptProductInfo.SharePathCode) == false)
            {
                // 获取自己的分享路径流节点
                var findCurNode = sharePathNodeEntList.FirstOrDefault(x => x.SharePathCode == ptProductInfo.SharePathCode && x.FxUserId == curFxUserId && x.UpFxUserId == supplierFxUserId && x.DownFxUserId == 0);
                string sharePathCode;
                var sharePathFlow = new SharePathFlow();
                // 如果不存在和厂家之间的分享路径流，则生成
                if (findCurNode == null)
                {
                    // 获取厂家分享路径流
                    var findSharePath = sharePathEntList.Find(x => x.SharePathCode == ptProductInfo.SharePathCode);
                    var findNode = sharePathNodeEntList.FindAll(x => x.SharePathCode == ptProductInfo.SharePathCode);
                    findSharePath.PathFlowNodes = findNode;

                    // 生成自己的分享路径流
                    sharePathFlow = SharePathFlowService.GenerateSharePathFlow(findSharePath, supplierFxUserId, curFxUserId);
                    sharePathCode = sharePathFlow.SharePathCode;
                }
                else
                    sharePathCode = findCurNode.SharePathCode;

                addModel.SharePathCode = sharePathCode;
                addModel.SharePathFlow = sharePathFlow;
            }

            if (string.IsNullOrEmpty(ptProductInfo.Ext.MainImageJson) == false)
            {
                addModel.ProductImagesStr = ptProductInfo.Ext.MainImageJson.ToObject<List<PtProductInfoImageModel>>().Select(a => a.ImageUrl).ToList();
            }
            addModel.RootNodeFxUserId = ptProductInfo.FromFxUserId;
            var productSkus = new List<BaseProductSkuModel>();
            foreach (var sku in skus)
            {
                var sharePathCode = sku.SharePathCode;
                var sharePathFlow = new SharePathFlow();
                if (isFromSupplier && string.IsNullOrEmpty(sku.SharePathCode) == false)
                {
                    // 获取自己的分享路径流节点
                    var findCurNode = sharePathNodeEntList.FirstOrDefault(x => x.SharePathCode == sku.SharePathCode && x.FxUserId == curFxUserId && x.UpFxUserId == supplierFxUserId && x.DownFxUserId == 0);
                    // 如果不存在和厂家之间的分享路径流，则生成
                    if (findCurNode == null)
                    {
                        // 获取厂家分享路径流
                        var findSharePath = sharePathEntList.Find(x => x.SharePathCode == sku.SharePathCode);
                        var findNode = sharePathNodeEntList.FindAll(x => x.SharePathCode == sku.SharePathCode);
                        findSharePath.PathFlowNodes = findNode;

                        // 生成自己的分享路径流
                        sharePathFlow = SharePathFlowService.GenerateSharePathFlow(findSharePath, supplierFxUserId, curFxUserId);
                        sharePathCode = sharePathFlow.SharePathCode;
                    }
                    else
                        sharePathCode = findCurNode.SharePathCode;
                }

                var productSkuModel = new BaseProductSkuModel
                {
                    SourceSkuCode = sku.SkuCode,
                    //如果存在则随机生成
                    SkuCode = existSkuCodes.Contains(sku.SkuCode) ? Guid.NewGuid().ToString().ToShortMd5() : sku.SkuCode,
                    Attribute = BaseProductUtils.FromJson(sku.Attributes),
                    ImageUrlStr = sku.ImageUrl,
                    Subject = sku.Subject,
                    SettlePrice = sku.DistributePrice, //厂家的分销价作为基础商品的采购价
                    UpFxUserId = supplierFxUserId,
                    UpSkuUid = supplierFxUserId > 0 ? sku.FromBaseProductSkuUid.ToString2() : "",
                    RootNodeFxUserId = sku.RootNodeFxUserId,
                    SharePathCode = sharePathCode,
                    PathNodeDeep = sku.PathNodeDeep,
                    SharePathFlow = sharePathFlow

                };
                if (isFromSupplier)
                    productSkuModel.PathNodeDeep = sku.PathNodeDeep + 1;
                productSkus.Add(productSkuModel);
            }
            addModel.ProductSkus = productSkus;

            addModel.ConfigModels = new List<BaseProductConfigModel>()
            {
                new BaseProductConfigModel
                {
                    RefType = "Product",
                    SupplierFxUserId = ptProductInfo.FromFxUserId,
                    RefCode = addModel.SpuCode,
                }
            };

            Log.Debug($"TransferPtProductInfoToBaseProduct:完成，addModel={addModel.ToJson()}", logFileName);

            return addModel;
        }

        #endregion

        public void UpdateProductStatusByKs(int productId, int status)
        {
            var listingTask = _repository.GetListingTaskRecordsByProductId(productId);
            if (listingTask == null)
            {
                return;
            }
            // 审核状态 0待审核 1审核待修改 2审核通过 3审核拒绝
            switch (status)
            {
                case 0: break;
                case 1:
                case 3:
                    if (listingTask.ListingProductStatus == (int)ListingProductStatusEnum.PutStashAudit)
                        listingTask.ListingProductStatus = (int)ListingProductStatusEnum.PutStashReject;

                    if (listingTask.ListingProductStatus == (int)ListingProductStatusEnum.ReadyForSaleAudit)
                        listingTask.ListingProductStatus = (int)ListingProductStatusEnum.ReadyForSaleReject;

                    break;
                case 2:
                    if (listingTask.ListingProductStatus == (int)ListingProductStatusEnum.PutStashAudit)
                        listingTask.ListingProductStatus = (int)ListingProductStatusEnum.PutStashPass;
                    
                    if (listingTask.ListingProductStatus == (int)ListingProductStatusEnum.ReadyForSaleAudit)
                        listingTask.ListingProductStatus = (int)ListingProductStatusEnum.ReadyForSalePass;

                    break;
            }

            Update(listingTask);
        }
    }
}
