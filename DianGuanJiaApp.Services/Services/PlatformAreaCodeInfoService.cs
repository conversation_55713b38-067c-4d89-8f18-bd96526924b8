using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using System.Collections;
using DianGuanJiaApp.Data.Model;
using System.Web;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;

namespace DianGuanJiaApp.Services
{

    public partial class PlatformAreaCodeInfoService : BaseService<PlatformAreaCodeInfo>
    {
        private PlatformAreaCodeInfoRepository _repository = new PlatformAreaCodeInfoRepository();

        public PlatformAreaCodeInfoService()
        {
            _repository = new PlatformAreaCodeInfoRepository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 不存在的插入，存在的忽略
        /// </summary>
        /// <param name="list"></param>
        public void BulkMerger(List<PlatformAreaCodeInfo> list)
        {
            if (list == null || !list.Any())
                return;

            var batchSize = 500;
            var count = Math.Ceiling(list.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchList = list.Skip(i * batchSize).Take(batchSize).ToList();

                //1.检查已存在的记录
                var uniqueKeys = batchList.Select(a => a.UniqueKey).ToList();
                var existInfos = GetListByUniqueKey(uniqueKeys, new List<string> { "PlatformType", "Type", "Code" });
                var insertList = new List<PlatformAreaCodeInfo>();
                if (existInfos != null && existInfos.Any())
                {
                    insertList = batchList.Where(a => !existInfos.Select(b => b.UniqueKey).Contains(a.UniqueKey)).ToList();
                }
                else
                    insertList = batchList;

                _repository.BulkWrite(insertList, "PlatformAreaCodeInfo");
            }

        }

        /// <summary>
        /// 根据uniqueKeys列表查询
        /// </summary>
        /// <param name="uniqueKeys"></param>
        /// <param name="fields">字段</param>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetListByUniqueKey(List<string> uniqueKeys, List<string> fields = null)
        {
            return _repository.GetListByUniqueKey(uniqueKeys, fields);
        }
        /// <summary>
        /// 获取区域
        /// </summary>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetAreaInfoList(string platformType, string parentKey)
        {
            return this.GetAreaInfoList(platformType)?.Where(a => a.ParentUniqueKey == parentKey)?.ToList();
        }

        private List<PlatformAreaCodeInfo> _cache = null;
        /// <summary>
        /// 获取区域
        /// </summary>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetAreaInfoList(string platformType)
        {
            if (_cache != null && _cache.Any())
                return _cache;
            else
            {
                var key = $"/System/Config/PlatformAreas/{platformType}";
                var cache = HttpRuntime.Cache[key] as List<PlatformAreaCodeInfo>;
                if (cache == null || !cache.Any())
                {
                    cache = _repository.GetListByPlatformType(platformType).ToList();
                    HttpRuntime.Cache.Insert(key, cache, null, DateTime.Now.AddHours(1), System.Web.Caching.Cache.NoSlidingExpiration);
                }
                _cache = cache;
                return cache;
            }
        }

        /// <summary>
        /// 获取省市区保存到一个List中
        /// </summary>
        /// <param name="platformType">指定平台</param>
        /// <param name="needSubfix">是否需要剔除后缀</param>
        /// <returns></returns>
        public List<PlatformProvinceModel> GetTreeAreaInfoList(string platformType, bool needSubfix = false)
        {
            var cache = _repository.GetTreeAreaInfos(platformType, needSubfix);
            return cache;
        }

        public PlatformAreaCodeInfo PlatformAreaByCode(long cityCode, string platformType)
        {
            return _repository.PlatformAreaByCode(cityCode, platformType);
        }
    }
}
