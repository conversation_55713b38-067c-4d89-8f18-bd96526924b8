using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using DianGuanJiaApp.Core.Helpers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.FxModel;
using static DianGuanJiaApp.SiteMessage.Services.MessageService;
using Newtonsoft.Json;
using DianGuanJiaApp.Utility.LockHandler;
using static DianGuanJiaApp.Data.Enum.SiteMessageTypeEnum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.OpenTelemetry;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{

    public partial class CommonSettingService : BaseService<CommonSetting>
    {
        private CommonSettingRepository _repository;

        public CommonSettingService()
        {
            _repository = new CommonSettingRepository();
            this._baseRepository = _repository;
        }

        public CommonSettingService(string connectionString)
        {
            _repository = new CommonSettingRepository(connectionString);
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 面单保密是否开启 键
        /// </summary>
        public static readonly string IsOpenBM = "IsOpenBM";
        /// <summary>
        /// 面单保密设置地址值 键
        /// </summary>
        public static readonly string BMValue = "BMValue";
        public static readonly string PrintContent = "PrintContent";
        public static readonly string KeywordFilter = "KeywordFilter";
        /// <summary>
        /// 打印序列号设置
        /// </summary>
        public static readonly string PrintSerialSet = "PrintSerialSet";

        /// <summary>
        /// 商品分类
        /// </summary>
        public static readonly string ProductCategory = "ProductCategory";
        /// <summary>
        /// 可合并订单提醒开关，true 或 false
        /// </summary>
        public static readonly string OrderMergerTip = "OrderMergerTip";
        /// <summary>
        /// 是否合并订单
        /// </summary>
        public static readonly string OrderMerger = "OrderMerger";
        /// <summary>
        /// 是否显示引导弹窗
        /// </summary>
        public static readonly string IsShowUserGuideDialogKey = "/User/Setting/IsShowUserGuideDialog";
        /// <summary>
        /// 主题名称ThemeName:New、Old
        /// </summary>
        public static readonly string ThemeName = "/User/Setting/ThemeName";
        /// <summary>
        /// 订单同步类型
        /// </summary>
        public static readonly string OrderSyncType = "/User/Setting/IsOrderSyncSameTime";
        /// <summary>
        /// 订单同步类型
        /// </summary>
        public static readonly string LastOrderSyncCheckTime = "/User/Setting/Fendan/LastOrderSyncCheckTime";
        /// <summary>
        /// 菜鸟批量打印数量
        /// </summary>
        public static readonly string CaiNiaoBatchPrintCount = "/User/Setting/CaiNiaoBatchPrintCount";
        /// <summary>
        /// 快手平台设置
        /// </summary>
        public static readonly string PlatformSettingKey = "/System/Setting/PlatformSetting";
        /// <summary>
        /// 关联店铺时立即迁移的最大单量
        /// </summary>
        public static readonly string MaxOrderCountToMigrateInTime = "/System/Setting/MaxOrderCountToMigrateInTime";
        /// <summary>
        /// 京东商品库最后同步时间
        /// </summary>
        public static readonly string JingDongProductLastSyncTime = "/JingDong/ProductLastSyncTime";

        /// <summary>
        /// 订单同步 时间及状态 存储在配置中的键，有些订单的同步没有在店铺表里维护同步状态，例如pdd 代发订单的同步状态
        /// </summary>
        public static readonly string SyncOrderParamterConfigKey = "/Sysnc/Order/SyncOrderParameter";
        /// <summary>
        /// 物流预警平台配置
        /// </summary>
        public static readonly string LogisticQueryEnableSetting = "/System/Setting/LogisticQueryEnableSetting";
        /// <summary>
        /// 快手行政区划库
        /// </summary>
        public const string KuaiShouOpenAddressDistrictList = "/Platform/Setting/OpenAddressDistrictList";

        /// <summary>
        /// 逻辑单订单展示信息配置
        /// </summary>
        public static readonly string OrderDisplaySetting = "/System/Setting/OrderDisplaySetting";

        /// <summary>
        /// 默认冷库ID配置,有多个用逗号分隔
        /// </summary>
        public static readonly string DefaultColdDbIdSetting = "/System/FenDan/DefaultColdDbId";

        /// <summary>
        /// 消息提醒设置
        /// </summary>
        public static readonly string ReminderSetting = "/User/Setting/ReminderSetting";

        /// <summary>
        /// 即时零售-仓库信息
        /// </summary>
        public static readonly string InstantRetailWarehouseKey = "InstantRetailWarehouse";

        /// <summary>
        /// 即时零售-仓库信息【全部】
        /// </summary>
        public static readonly string InstantRetailWarehouseAllKey = "InstantRetailWarehouseAll";


        public static OrderDisplaySetting _defaultOrderDisplaySetting = null;
        /// <summary>
        /// 默认逻辑单订单展示信息配置
        /// </summary>
        public static OrderDisplaySetting DefaultOrderDisplaySetting
        {
            get
            {
                if (_defaultOrderDisplaySetting == null)
                {
                    //设置默认值
                    _defaultOrderDisplaySetting = new OrderDisplaySetting() { ProductSetting = "DisplayProductShortTitle", BizCodeSetting = "DisplayBizCode", RecipientSetting = "DisplayRecipient" };
                    //_defaultOrderDisplaySetting = new OrderDisplaySetting() { ProductSetting = "DisplayProductShortTitle", SkuSetting = "DisplaySkuShortTitle", BizCodeSetting = "DisplayBizCode", RecipientSetting = "DisplayRecipient" };
                }
                return _defaultOrderDisplaySetting;
            }
        }


        /// <summary>
        /// 打印批次序号
        /// </summary>
        const string dayPrintBatchNumberKey = "DayPrintBatchNumber", dayPrintBatchDateKey = "DayPrintBatchDate";

        /// <summary>
        /// 更新流水号计数
        /// </summary>
        /// <param name="settingStr"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool UpdatePrintSerialNumber(string settingStr, int shopId)
        {
            return Set(PrintSerialSet, settingStr, shopId) > 0;
            //return _repository.UpdatePrintSerialNumber(settingStr, shopId);
        }

        /// <summary>
        /// 获取打印流水号配置信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public PrintSerialSetting GetPrintSerialSetting(int shopId)
        {
            var printSerialSetting = Get<PrintSerialSetting>(PrintSerialSet, shopId);
            if (printSerialSetting != null)
            {
                var clearFormat = printSerialSetting.ClearSet.ToString2();
                if (!clearFormat.IsNullOrEmpty())
                {
                    var key = DateTime.Now.ToString(clearFormat);
                    var printSerialValue = printSerialSetting.PrintSerialValues.FirstOrDefault(m => m.DateTime == key);
                    if (printSerialValue == null)
                    {
                        printSerialValue = new PrintSerisalValue() { DateTime = key, SerialNum = 0 };
                        printSerialSetting.PrintSerialValues.Add(printSerialValue);
                    }
                }
            }
            else
            {
                printSerialSetting = new PrintSerialSetting()
                {
                    ClearSet = "yyyy-01-01 00:00:00",
                    SerialFormat = "MMdd",
                    ContainerDate = true,
                    ContainerPageNumber = true,
                    PrintSerialValues = new List<PrintSerisalValue>()
                };
                var json = JsonExtension.ToJson(printSerialSetting);
                var result = Set(PrintSerialSet, json, shopId);
                if (result == 0)
                    printSerialSetting = null;
            }
            return printSerialSetting;
        }

        /// <summary>
        /// 按照KEY获取所有店铺配置
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public List<CommonSetting> GetList(string key)
        {
            return _repository.GetList(key);
        }

        /// <summary>
        /// 特殊情况用 根据 Key 模糊
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public List<CommonSetting> GetListByKeyEndsWithLike(string key)
        {
            return _repository.GetListByKeyEndsWithLike(key);
        }
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public T Get<T>(string key, int shopId, bool isUseCache = true) where T : class
        {
            var json = Get(key, shopId, secondCacheExpireMinutes: 15)?.Value;
            if (string.IsNullOrEmpty(json))
                return default(T);
            else
                return json.ToObject<T>();
        }

        /// <summary>
        /// 获取配置（按键，店铺ID） 内部包含Redis缓存以及二级缓存仅支持ShopId少于等于0
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <param name="secondCacheExpireMinutes">系统配置二级缓存有效期，默认5分钟</param>
        /// <returns></returns>
        public CommonSetting Get(string key, int shopId, bool isUseCache = true, int secondCacheExpireMinutes = 5)
        {
            return _repository.Get(key, shopId, isUseCache, secondCacheExpireMinutes);
            //return _repository.Get(" where ShopId IN @shopId AND [Key]=@key", new { key, shopId = new List<int> { 0, shopId } })?.OrderByDescending(s => s.ShopId)?.FirstOrDefault();
        }

        /// <summary>
        /// 获取配置，内部没有系统配置二级缓存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public CommonSetting GetSettingWithCache(string key, int shopId, bool isUseCache = true)
        {
            return _repository.GetSettingWithCache(key, shopId, isUseCache);
        }

        public void CheckSettingRedis(string key, List<int> shopIds)
        {
            _repository.CheckSettingRedis(key, shopIds);
        }

        /// <summary>
        /// 获取多个配置Model
        /// </summary>
        /// <param name="keys"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public List<CommonSetting> GetSets(List<string> keys, int shopId)
        {
            return _repository.GetSets(keys, shopId);
            //return _repository.Get(" where ShopId IN @shopId AND [Key] IN@keys", new { keys, shopId = new List<int> { shopId } })?.OrderByDescending(s => s.ShopId)?.ToList();
        }

        /// <summary>
        /// 获取多个配置Model
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseRedisCache"></param>
        /// <param name="isUseMemoryCache"></param>
        /// <returns></returns>
        public CommonSetting GetSet(string key, int shopId, bool isUseRedisCache = false, bool isUseMemoryCache = false)
        {
            var setting = new CommonSetting();
            setting.Key = key;
            setting.ShopId = shopId;
            setting.Value = null;

            // 系统缓存
            if (isUseMemoryCache)
            {
                var emoryCacheKey = $"{key}/{shopId}";
                var emoryCacheSeconds = 12 * 60 * 60d;

                // 系统缓存已存在
                if (CacheHelper.Contains(emoryCacheKey))
                {
                    var emoryCacheValue = CacheHelper.Get<string>(emoryCacheKey);
                    setting.Value = emoryCacheValue;
                    return setting;
                }
                // 系统缓存不存在
                else
                {
                    if (!CacheHelper.Contains(emoryCacheKey))
                    {
                        setting = _repository.GetSettingWithCache(key, shopId, isUseRedisCache);
                        if (setting != null)
                            CacheHelper.Set(emoryCacheKey, setting.Value, emoryCacheSeconds);
                    }
                }
            }
            // 数据库缓存
            else
            {
                setting = _repository.GetSettingWithCache(key, shopId, isUseRedisCache);
            }
            return setting;
        }

        public List<CommonSetting> GetSettingByShopIds(string key, List<int> shopIds)
        {
            return _repository.Get(key, shopIds);
            //return _repository.Get(" where ShopId IN@shopIds AND [Key] = @key", new { key, shopIds })?.ToList();
        }

        public List<CommonSetting> GetSettingModelByShopIds(string key, List<int> shopIds, bool isUseCache = true)
        {
            var settings = ProcessInBatches(shopIds, shopId => _repository.GetSets(key, shopId, isUseCache))?
                .Where(s => s != null)
                .ToList();
            return settings;
        }

        /// <summary>
        /// 获取多个配置Model
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopIds"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public List<T> GetSettingByShopIds<T>(string key, List<int> shopIds, bool isUseCache = true)
        {
            var settings = _repository.GetSets(key, shopIds, isUseCache)?.Select(s =>
            {
                if (s == null || string.IsNullOrEmpty(s?.Value))
                    return default(T);
                else
                    return s.Value.ToObject<T>();
            })
                .Where(s => s != null)
                .ToList();
            return settings;
        }


        /// <summary>
        /// 获取配置布尔值
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool GetBool(string key, int shopId)
        {
            return Get(key, shopId)?.Value?.ToBool() ?? false;
        }

        /// <summary>
        /// 获取配置字符串值
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public string GetString(string key, int shopId, bool isUseCache = true)
        {
            return Get(key, shopId, isUseCache)?.Value;
        }

        /// <summary>
        /// 获取配置的整型值
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="defaultValue">默认值</param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public int GetInt(string key, int shopId=0, int defaultValue = default, bool isUseCache = true)
        {
            var value = Get(key, shopId, isUseCache)?.Value?.ToInt();
            return value??defaultValue;
        }
        /// <summary>
        /// 获取配置字符串值（仅店铺ID）
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public string GetStringOnlyByShopId(string key, int shopId, bool isUseCache = true)
        {
            var commonSetting = Get(key, shopId, isUseCache);
            return commonSetting?.ShopId == 0 ? null : commonSetting?.Value;
        }

        /// <summary>
        /// 全局通用配置设置：
        /// </summary>
        /// <param name="key">配置键 必填且不能为空</param>
        /// <param name="value">配置值</param>
        /// <param name="shopId">店铺ID 必填且必须大于0（0表示系统默认配置，禁止修改）</param>
        /// <returns></returns>
        public int Set(string key, string value, int shopId)
        {
            if (string.IsNullOrEmpty(key))
                throw new LogicException("请提供配置的键");
            //是否包含在仅添加到Redis中的键
            if (shopId > 0 && _repository.CommonSettingOnlyInRedisKeys.Contains(key))
            {
                var isForeverEffective = _repository.CommonSettingOnlyInRedisKeyIsForeverEffective(key);
                var setting = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                    IsForeverEffective = isForeverEffective
                };
                return _repository.OnlyAddToRedis(setting);
            }

            //需要数据库和Redis都要保存
            var model = Get(key, shopId);
            if (model == null || model.ShopId == 0)
            {
                model = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                };
                model.Id = _repository.Add(model);
            }
            else
            {
                //数据一样不做更新
                if (model.Value == value)
                    return model.Id;
                model.Value = value;
                _repository.Update(model);
            }
            return model.Id;
        }

        /// <summary>
        /// 配置设置 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int Set(CommonSetting model)
        {
            return Set(model.Key, model.Value, model.ShopId);
        }

        /// <summary>
        /// 配置设置 
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="shopId"></param>
        /// <param name="isUseCache"></param>
        /// <returns></returns>
        public int Set(string key, string value, int shopId, bool isUseCache)
        {
            if (string.IsNullOrEmpty(key))
                throw new LogicException("请提供配置的键");
            //是否包含在仅添加到Redis中的键
            if (shopId > 0 && _repository.CommonSettingOnlyInRedisKeys.Contains(key))
            {
                var isForeverEffective = _repository.CommonSettingOnlyInRedisKeyIsForeverEffective(key);
                var setting = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                    IsForeverEffective = isForeverEffective
                };
                return _repository.OnlyAddToRedis(setting);
            }

            var model = Get(key, shopId, isUseCache);
            if (model == null || model.ShopId == 0)
            {
                model = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                };
                model.Id = _repository.Add(model);
            }
            else
            {
                //数据一样不做更新
                if (model.Value == value)
                    return model.Id;
                model.Value = value;
                _repository.Update(model);
            }
            return model.Id;
        }
        /// <summary>
        /// 全局通用配置设置：
        /// </summary>
        /// <param name="key">配置键 必填且不能为空</param>
        /// <param name="value">配置值</param>
        /// <param name="shopId">店铺ID 必填且必须大于0（0表示系统默认配置，禁止修改）</param>
        /// <returns></returns>
        public int SetPdd(string key, string value, int shopId)
        {
            if (string.IsNullOrEmpty(key))
                throw new LogicException("请提供配置的键");
            var model = _repository.GetPdd(key, shopId);
            if (model == null || model.ShopId == 0)
            {
                model = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                };
                model.Id = _repository.AddPdd(model);
            }
            else
            {
                model.Value = value;
                _repository.UpdatePdd(model);
            }
            return model.Id;
        }


        /// <summary>
        /// 全局通用配置设置：设置到抖店云，目前仅抖店云推送库需要使用，因为抖店的授权还在聚石塔，但店铺迁移至抖店了
        /// </summary>
        /// <param name="key">配置键 必填且不能为空</param>
        /// <param name="value">配置值</param>
        /// <param name="shopId">店铺ID 必填且必须大于0（0表示系统默认配置，禁止修改）</param>
        /// <returns></returns>

        public int SetAndSyncToTouTiao(string key, string value, int shopId)
        {
            var id = Set(key, value, shopId);
            try
            {
                SetToTouTiao(key, value, shopId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"更新CommonSetting到头条时发生错误：{ex}");
            }
            return id;
        }

        /// <summary>
        /// 获取配置Model
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public CommonSetting GetFromTouTiaoCloud(string key, int shopId)
        {
            return _repository.GetFromTouTiaoCloud(key, shopId);
        }

        /// <summary>
        /// 获取配置Model
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public int AddToTouTiaoCloud(CommonSetting model)
        {
            return _repository.AddToTouTiaoCloud(model);
        }

        /// <summary>
        /// 获取配置Model
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public void UpdateToTouTiaoCloud(CommonSetting model)
        {
            _repository.UpdateToTouTiaoCloud(model);
        }

        /// <summary>
        /// 全局通用配置设置：设置到抖店云，目前仅抖店云推送库需要使用，因为抖店的授权还在聚石塔，但店铺迁移至抖店了
        /// </summary>
        /// <param name="key">配置键 必填且不能为空</param>
        /// <param name="value">配置值</param>
        /// <param name="shopId">店铺ID 必填且必须大于0（0表示系统默认配置，禁止修改）</param>
        /// <returns></returns>
        public int SetToTouTiao(string key, string value, int shopId)
        {
            if (string.IsNullOrEmpty(key))
                throw new LogicException("请提供配置的键");
            var model = GetFromTouTiaoCloud(key, shopId);
            if (model == null || model.ShopId == 0)
            {
                model = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                };
                model.Id = _repository.AddToTouTiaoCloud(model);
            }
            else
            {
                //数据一样不做更新
                if (model.Value == value)
                    return model.Id;
                model.Value = value;
                _repository.UpdateToTouTiaoCloud(model);
            }
            return model.Id;
        }

        /// <summary>
        /// 全局通用配置设置：设置到拼多多云
        /// </summary>
        /// <param name="key">配置键 必填且不能为空</param>
        /// <param name="value">配置值</param>
        /// <param name="shopId">店铺ID 必填且必须大于0（0表示系统默认配置，禁止修改）</param>
        /// <returns></returns>
        public int SetToPdd(string key, string value, int shopId)
        {
            if (string.IsNullOrEmpty(key))
                throw new LogicException("请提供配置的键");
            var model = _repository.GetFromPddCloud(key, shopId);
            if (model == null || model.ShopId == 0)
            {
                model = new CommonSetting
                {
                    Key = key,
                    Value = value,
                    ShopId = shopId,
                };
                model.Id = _repository.AddToPddCloud(model);
            }
            else
            {
                //数据一样不做更新
                if (model.Value == value)
                    return model.Id;
                model.Value = value;
                _repository.UpdateToPddCloud(model);
            }

            return model.Id;
        }

        public SystemSetting GetSystemSetting(int shopId)
        {
            var settings = GetSets(new List<string> { OrderMerger, OrderMergerTip, CaiNiaoBatchPrintCount }, shopId);
            var merger = settings?.FirstOrDefault(s => s.Key == OrderMerger)?.Value;
            var tip = settings?.FirstOrDefault(s => s.Key == OrderMergerTip);
            var batchCount = settings?.FirstOrDefault(s => s.Key == CaiNiaoBatchPrintCount)?.Value?.ToInt() ?? 0;
            var set = new SystemSetting();
            //var merger = Get(OrderMerger, shopId)?.Value;
            set.IsNotAutoMerger = merger == "false";
            //var tip = Get(OrderMergerTip, shopId);
            set.IsShowMergerTips = tip == null || string.IsNullOrEmpty(tip?.Value) || tip.Value == "true";
            //var batchCount = Get(CaiNiaoBatchPrintCount, shopId)?.Value?.ToInt() ?? 0;
            if (batchCount > 0)
                set.CaiNiaoBatchPrintCount = batchCount;
            return set;
        }

        public CommentSettingModel GetCommentSetting(int shopId)
        {
            var set = new CommentSettingModel();
            var commentSet = Get("CommentSet", shopId);
            if (commentSet != null && !commentSet.Value.IsNullOrEmpty())
                set = JsonExtension.ToObject<CommentSettingModel>(commentSet.Value);
            return set;
        }

        //public void TransferCommenSettingToConfigDb()
        //{
        //    _repository.TransferCommenSettingToConfigDb();
        //}

        /// <summary>
        /// 检查
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool IsShowUserGuideDialog(int shopId)
        {
            var value = Get(IsShowUserGuideDialogKey, shopId)?.Value;
            if (string.IsNullOrEmpty(value))
                return true;
            else
                return value.ToBool();
        }

        /// <summary>
        /// 检查用户是否使用的旧版主题
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool IsUseOldTheme(int shopId)
        {
            var value = Get(ThemeName, shopId)?.Value;
            if (string.IsNullOrEmpty(value))
                return false;
            else
                return value.ToLower() == "old";
        }

        /// <summary>
        /// 获取日志配置信息,
        /// </summary>
        /// <returns>返回需要记录日志的操作名称，默认返回*，表示所有的</returns>
        public List<LogConfigModel> GetLogConfig()
        {
            // var key = "/System/Config/FenDan/LogConfig";
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToObject<List<LogConfigModel>>();
            // else
            // {
            //     var config = Get<List<LogConfigModel>>(key, 0);
            //     if (config == null)
            //         config = new List<LogConfigModel>() { new LogConfigModel { OperationType = "*", IsEnabled = true } };
            //     if (!config.Any())
            //         config.Add(new LogConfigModel { OperationType = "*", IsEnabled = true });
            //     HttpRuntime.Cache.Insert(key, config.ToJson(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     //var value = HttpRuntime.Cache[key].ExtToString().ToObject<List<string>>();
            //     return config;
            // }

            const string key = "/System/Config/FenDan/LogConfig";
            var config = Get<List<LogConfigModel>>(key, 0);
            if (config == null)
                config = new List<LogConfigModel> { new LogConfigModel { OperationType = "*", IsEnabled = true } };
            if (!config.Any())
                config.Add(new LogConfigModel { OperationType = "*", IsEnabled = true });
            return config;
        }

        /// <summary>
        /// 获取平台配置信息,
        /// </summary>
        /// <returns>返回配置信息，有缓存，15分钟过期</returns>
        public PlatformSettingModel GetPlatformSetting(string platformType)
        {
            // var key = PlatformSettingKey + "/" + platformType;
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToObject<PlatformSettingModel>();
            // else
            // {
            //     var config = Get<PlatformSettingModel>(key, 0);
            //     if (config == null)
            //         config = new PlatformSettingModel();
            //     HttpRuntime.Cache.Insert(key, config.ToJson(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return config;
            // }


            var key = PlatformSettingKey + "/" + platformType;
            var config = Get<PlatformSettingModel>(key, 0) ?? new PlatformSettingModel();
            return config;
        }

        /// <summary>
        /// 获取最大同步订单数量，单次同步数量超过
        /// </summary>
        /// <returns></returns>
        public int GetMaxSyncOrderCount()
        {
            // var key = "/System/Config/MaxSyncOrderCount";
            // var defaultValue = 50000;
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToInt();
            // else
            // {
            //     var config = Get(key, 0)?.Value?.ToInt() ?? 0;
            //     if (config <= 0)
            //         config = defaultValue;
            //     HttpRuntime.Cache.Insert(key, config, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return config;
            // }

            const string key = "/System/Config/MaxSyncOrderCount";
            const int defaultValue = 50000;
            var config = Get(key, 0)?.Value?.ToInt() ?? 0;
            if (config <= 0)
                config = defaultValue;
            return config;
        }

        public int GetMaxOnePageSyncOrderCount(Shop shop)
        {
            var key = "MaxOnePageSyncOrderCount";
            var defaultValue = 4900;
            var set = GetFxDefaultSetting(shop.PlatformType, key, shop.Id);
            if (set != null)
            {
                var maxCount = set.Value.ToInt();
                maxCount = maxCount > 0 ? maxCount : defaultValue;
                if (shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                    maxCount = maxCount > 50000 ? 49999 : maxCount;//抖店接口限制最多不能超过5w单
                return maxCount;
            }
            return defaultValue;
        }

        public int GetMaxSyncOrderCount(Shop shop)
        {
            var key = "MaxSyncOrderCount";
            var defaultValue = 50000;
            var set = GetFxDefaultSetting(shop.PlatformType, key, shop.Id);
            if (set != null)
            {
                var maxCount = set.Value.ToInt();
                maxCount = maxCount > 0 ? maxCount : defaultValue;
                return maxCount;
            }
            return defaultValue;
        }


        public FunctionSettingModel GetFunctionSetting(FunctionSettingModel functionSetting, int shopId)
        {
            var key = "/Shop/FunctionSetting/" + shopId;
            var cache = HttpRuntime.Cache[key].ExtToString();
            if (!string.IsNullOrEmpty(cache))
            {
                functionSetting = cache.ToObject<FunctionSettingModel>();
                return functionSetting;
            }
            var t = functionSetting.GetType();
            var keys = t.GetProperties()?.Select(p => $"/Shop/FunctionSetting/{p.Name}")?.ToList();
            if (keys != null && keys.Any())
            {
                var css = this.GetSets(keys, shopId);
                if (css != null && css.Any())
                {
                    foreach (var cs in css)
                    {
                        var p = t.GetProperty(cs.Key.Replace("/Shop/FunctionSetting/", ""));
                        if (p != null)
                        {
                            var value = (cs?.Value ?? "").ToLower();
                            var trueValue = new string[] { "1", "true", "enabled", "ok", "yes" };
                            if (trueValue.Contains(value))
                                p.SetValue(functionSetting, true);
                        }
                    }
                }
                HttpRuntime.Cache.Insert(key, functionSetting.ToJson(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            }
            return functionSetting;
        }

        public int GetMaxOrderCountToMigrateInTime()
        {
            var count = Get(MaxOrderCountToMigrateInTime, 0)?.Value?.ToInt();
            if (count > 0)
                return count.Value;
            else
                return 5000;
        }

        /// <summary>
        /// 获取推送库配置信息
        /// </summary>
        /// <param name="shopVersion"></param>
        /// <param name="isEnabledPushDb"></param>
        /// <param name="shopId"></param>
        /// <returns>参数1：当前店铺版本是否启用推送库，参数2：推送库是否停止，若推送库停止，参数1也是false</returns>
        public Tuple<bool, bool> GetTaobaoPushDbSetting(string shopVersion = "", bool isEnabledPushDb = false,
            int? shopId = null)
        {
            // var cacheKey = $"/Cache/GetTaobaoPushDbSetting/{shopVersion}";
            // var cache = HttpRuntime.Cache[cacheKey];
            // if (cache != null)
            // {
            //     var result = cache as Tuple<bool, bool>;
            //     if (shopId.HasValue)
            //     {
            //         Log.WriteLine(
            //             $"淘宝店铺ID【{shopId.Value}】推送库开关，命中缓存，开关值：{result.ToJson()}，其他信息：{shopVersion}|{isEnabledPushDb}",
            //             $"TaobaoPushDbSettingSwitch_{DateTime.Now:yyyy-MM-dd}.log");
            //     }
            //
            //     return result;
            // }

            if (string.IsNullOrEmpty(shopVersion) || shopVersion == "0")
            {
                shopVersion = "";
            }
            var key1 = $"/System/Config/Taobao/IsTaobaoPushDbEnabled/{shopVersion}";
            var key2 = $"/System/Config/Taobao/IsTaobaoPushDbStoped";
            var css = GetSets(new List<string> { key1, key2 }, 0);
            var value1 = css.FirstOrDefault(x => x.Key == key1)?.Value ?? "";
            var value2 = css.FirstOrDefault(x => x.Key == key2)?.Value ?? "";
            var trueResults = new List<string> { "true", "1", "yes", "ok" };
            var item1 = false;
            var item2 = false;
            if (trueResults.Contains(value1) && isEnabledPushDb)
                item1 = true;
            if (trueResults.Contains(value2))
                item2 = true;
            //推送库停止服务了，则所有查询都不能走推送库了
            if (item2 == true)
                item1 = false;
            var res = new Tuple<bool, bool>(item1, item2);
            // if (shopId.HasValue)
            // {
            //     Log.WriteLine(
            //         $"淘宝店铺ID【{shopId.Value}】推送库开关，未命中缓存，开关值：{res.ToJson()}，配置值：{value1}|{value2}，其他信息：{shopVersion}|{isEnabledPushDb}",
            //         $"TaobaoPushDbSettingSwitch_{DateTime.Now:yyyy-MM-dd}.log");
            // }
            // HttpRuntime.Cache.Insert(cacheKey, res, null, DateTime.Now.AddMinutes(5),
            //     System.Web.Caching.Cache.NoSlidingExpiration);
            return res;
        }

        /// <summary>
        /// 获取消息配置字符串，若不存在 则创建新的配置
        /// </summary>
        /// <param name="settingKey"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public string GetOrNewMessageSetting(string settingKey, int shopId, out bool isNew)
        {
            isNew = false;
            try
            {
                //var tt = new MessageSubscribeSetting();
                //tt.MessageTypeSwitch = null;
                var settingValue = GetString(settingKey, shopId);
                //var settingValue = string.Empty;

                if (settingValue.IsNullOrEmpty()) // 初次获取，不存在，创建新的配置
                {
                    settingValue = new MessageSubscribeSetting().ToJson();
                    isNew = true;
                }
                else
                {
                    var settingObject = JsonConvert.DeserializeObject<MessageSubscribeSetting>(settingValue);
                    if (settingObject != null && settingObject.MessageTypeSwitch.IsNullOrEmpty()) // 存在，但MessageTypeSwitch为空，初始化
                    {
                        if (!settingObject.IsEnabled)
                            settingValue = new MessageSubscribeSetting(isEnabled: false).ToJson(); // 全局配置关闭订阅
                        else
                            settingValue = new MessageSubscribeSetting().ToJson();// 全局配置开启订阅
                        isNew = true;
                    }
                    //旧数据兼容 移除规格变更配置
                    if (settingObject != null && settingObject.MessageTypeSwitch.IsNotNullAndAny())
                    {
                        settingObject.MessageTypeSwitch = settingObject.MessageTypeSwitch.Where(t => t.FirstType != PrimaryType.SkuChanged.ToString2()).ToList();
                        settingValue = settingObject.ToJson();
                    }
                }
                return settingValue;

            }
            catch (Exception ex)
            {
                Log.WriteError($"用户{SiteContext.Current.CurrentFxUserId}获取消息订阅配置发生异常,shopId{shopId}，{ex.StackTrace}");
                throw ex;
            }

        }

        /// <summary>
        /// 获取备注设置字符串，若不存在 则创建新的配置
        /// </summary>
        /// <param name="settingKey"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public string GetOrNewRemarkSetting(string settingKey, int shopId, out bool isNew, out RemarkSettingModel model)
        {
            isNew = false;
            try
            {
                var settingValue = GetString(settingKey, shopId);

                if (settingValue.IsNullOrEmpty()) // 初次获取，不存在，创建新的配置
                {
                    model = new RemarkSettingModel();
                    settingValue = model.ToJson();
                    isNew = true;
                }
                else
                {
                    model = JsonConvert.DeserializeObject<RemarkSettingModel>(settingValue);
                }
                return settingValue;

            }
            catch (Exception ex)
            {
                Log.WriteError($"用户{SiteContext.Current.CurrentFxUserId}获取备注配置发生异常,shopId{shopId}，{ex.StackTrace}");
                throw ex;
            }

        }

        /// <summary>
        /// 获取抖店推送库配置信息
        /// </summary>
        public TouTiaoPushDbInfo GetTouTiaoPushDbSetting(Shop shop)
        {
            try
            {
                //1.查询应用是否启用了推送库
                //2.未启用：则直接走api，启用：则判断店铺的推送库情况
                //3.店铺通过接口获取推送库
                var appKey = shop.AppKey;
                var isEnabeldPushDbKey = $"/TouTiao/IsEnabeldPushDb/{appKey}"; //应用是否开启推送库
                var shopPushDbKey = $"/TouTiao/PushDb/{appKey}"; //店铺推送库配置
                var shopPushDbOpenTimeKey = $"/TouTiao/PushDb/OpenTime/{appKey}"; //推送库开启时间
                var singleSyncOrderUsePushDbKey = $"/Temp/TouTiao/PushDb/{appKey}"; //单个同步是否使用送库
                var productSyncUsePushDbKey = $"/TouTiao/PushDb/ProductSync/{appKey}"; //商品是否使用推送库，全局配置

                //查询应用是否开启了推送库
                var appIsEnabelPushDb = GetBool(isEnabeldPushDbKey, 0);
                //全局开关已关闭，跳出
                if (appIsEnabelPushDb == false)
                    return null;

                var shopPushDbId = GetStringOnlyByShopId(shopPushDbKey, shop.Id); //获取店铺的推送库配置

                //店铺推送库如果未开启，判断一下消息是否走推送库
                //有这个配置，说明单个同步需要使用推送库.这么做的原因：前期推送库开启后，增量同步还是走接口，消息走推送库同步
                var singleSyncOrderUsePushDbId = shopPushDbId;
                if (string.IsNullOrWhiteSpace(singleSyncOrderUsePushDbId))
                {
                    singleSyncOrderUsePushDbId = GetStringOnlyByShopId(singleSyncOrderUsePushDbKey, shop.Id); //获取店铺单个同步的推送库配置
                }

                //店铺没有配置，判断下全局开关是否有开启
                if (string.IsNullOrWhiteSpace(shopPushDbId) && string.IsNullOrWhiteSpace(singleSyncOrderUsePushDbId))
                {
                    return null;
                }

                var pushDbConnectionStr = string.Empty;
                if (string.IsNullOrWhiteSpace(shopPushDbId) == false || string.IsNullOrEmpty(singleSyncOrderUsePushDbId) == false)
                {
                    var pushDbId = string.IsNullOrWhiteSpace(shopPushDbId) ? singleSyncOrderUsePushDbId : shopPushDbId;
                    //获取推送库链接
                    pushDbConnectionStr = GetString($"/TouTiao/PushDbConnetion/{pushDbId}", 0);
                }

                //推送库的连接字符串未配置
                if (string.IsNullOrWhiteSpace(pushDbConnectionStr))
                {
                    //系统配置异常，钉钉通知
                    Utility.Net.HttpUtility.PostToDingDing($"头条应用【{shop.AppKey}】的推送库【{shopPushDbId}】链接字符串未配置，请检查系统配置【/TouTiao/PushDbConnetion/{shopPushDbId}】！", new List<string> { "13065187972", "18026946819" });
                    return null;
                }

                //判断增量同步是否使用推送库
                //1.用户开启了推送库，2.用户增量同步时间大于推送库开启时间（需要这个条件的原因：推送库为了节省空间，设置不推送历史数据）
                var incrementSyncUsePushDb = false; //增量同步走推送库
                var productIncrementSyncUsePushDb = false; //商品增量同步走推送库
                //售后单增量同步走推送库
                var afterOrderIncrementSyncUsePushDb = false;
                DateTime? pushDbOpenTime = null;
                if (!string.IsNullOrWhiteSpace(shopPushDbId))
                {
                    //读取用户推送库开启时间
                    var pushDbOpenTimeSettings = this.GetString(shopPushDbOpenTimeKey, shop.Id);
                    pushDbOpenTime = string.IsNullOrWhiteSpace(pushDbOpenTimeSettings) ? DateTime.Now : DateTime.Parse(pushDbOpenTimeSettings);
                    var shopLastSyncTime = shop.OrderSyncStatus?.LastSyncTime; //?? shop.LastSyncTime;
                    incrementSyncUsePushDb = shopLastSyncTime > pushDbOpenTime; //增量同步开始时间是否大于推送库开启时间

                    var shopProductLastSyncTime = shop.ProductSyncStatus?.LastSyncTime;// ?? shop.LastSyncTime;
                    productIncrementSyncUsePushDb = shopProductLastSyncTime > pushDbOpenTime; //商品增量同步开始时间是否大于推送库开启时间

                    var lastSyncTimeByAfterOrder = shop.AfterSaleSyncStatus?.LastSyncTime;
                    afterOrderIncrementSyncUsePushDb = lastSyncTimeByAfterOrder > pushDbOpenTime;
                }

                var productSyncUsePushDb = this.GetBool(productSyncUsePushDbKey, 0);
                var touTiaoPushDbInfo = new TouTiaoPushDbInfo
                {
                    ConnetionString = pushDbConnectionStr,
                    RdsId = shopPushDbId,
                    PushDbOpenTime = pushDbOpenTime,
                    IncrementSyncUsePushDb = incrementSyncUsePushDb,
                    SingleOrderSyncUsePushDb = !string.IsNullOrWhiteSpace(singleSyncOrderUsePushDbId),
                    ProductSyncUsePushDb = productSyncUsePushDb,
                    ProductIncrementSyncUsePushDb = productIncrementSyncUsePushDb,
                    AfterOrderIncrementSyncUsePushDb = afterOrderIncrementSyncUsePushDb
                };
                Log.Debug(
                    () =>
                        $"shop.ProductSyncStatus={shop.ProductSyncStatus.ToJson()},touTiaoPushDbInfo={touTiaoPushDbInfo.ToJson()}",
                    $"TouTiao同步日志-{shop.Id}.txt");
                //返回推送库相关信息
                return touTiaoPushDbInfo;
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取头条店铺【{shop.Id}】的推送库配置报错:{ex}");
                return null;
            }
        }
        /// <summary>
        /// 获取头条推送库开启时间
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public DateTime? GetTouTiaoPushDbOpenTime(Shop shop)
        {
            var appKey = shop.AppKey;
            //推送库开启时间
            var shopPushDbOpenTimeKey = $"/TouTiao/PushDb/OpenTime/{appKey}";
            //读取用户推送库开启时间
            var pushDbOpenTimeSettings = this.GetString(shopPushDbOpenTimeKey, shop.Id);
            if (string.IsNullOrWhiteSpace(pushDbOpenTimeSettings))
            {
                return null;
            }
            return DateTime.Parse(pushDbOpenTimeSettings);
        }
        /// <summary>
        /// 淘宝同步是否使用游标方式，默认false
        /// </summary>
        /// <returns></returns>
        public bool IsTaobaoSyncUseCursor()
        {
            var value = Get("/System/Config/Taobao/IsTaobaoSyncUseCursor", 0)?.Value?.ToLower();
            var trueResults = new List<string> { "true", "1", "yes", "ok" };
            if (string.IsNullOrEmpty(value) == false && trueResults.Contains(value))
                return true;
            return false;
        }

        public List<CommonSetting> GetNeedCheckOrderShopSettings(string pt, string key)
        {
            var sql = $@"	SELECT cs.Id,s.Id AS ShopId,cs.Value
FROM dbo.P_Shop s WITH(NOLOCK)
    LEFT JOIN dbo.P_CommonSetting cs WITH(NOLOCK)
        ON s.Id = cs.ShopId
           AND cs.[Key] = '{key}'
WHERE s.PlatformType = '{pt}'
      AND
      (
          s.ExpireTime IS NULL
          OR s.ExpireTime < GETDATE()
          OR s.LastSyncMessage LIKE '%】授权过期%'
      )
      AND
      (
          cs.Value IS NULL
          OR cs.Value != 'true'
      )";
            return _repository.GetSettings(sql);
        }

        public List<CommonSetting> GetTestCheckOrderShopSettings(string pt, string key)
        {
            //最后刷新token时间，拼多多的是24小时过期，淘宝的是8小时过期
            var compareTime = DateTime.Now.AddHours(-4).ToString("yyyy-MM-dd");
            if (pt == PlatformType.Pinduoduo.ToString())
                compareTime = DateTime.Now.AddHours(-12).ToString("yyyy-MM-dd");
            var sql = $@"	SELECT cs.Id,s.Id AS ShopId,cs.Value
FROM dbo.P_Shop s WITH(NOLOCK)
    LEFT JOIN dbo.P_CommonSetting cs WITH(NOLOCK)
        ON s.Id = cs.ShopId
           AND cs.[Key] = '{key}'
WHERE s.PlatformType = '{pt}'
      AND s.ExpireTime > GETDATE()
	  AND (s.LastSyncMessage NOT LIKE '%】授权过期%' OR( (s.LastSyncTime IS NULL AND s.CreateTime<DATEADD(DAY,-3,GETDATE()) ) OR (s.LastSyncTime<DATEADD(DAY,-3,GETDATE())) ) )
      AND (s.LastRefreshTokenTime IS NULL OR s.LastRefreshTokenTime<'{compareTime}')
      AND
      (
          cs.Value IS NULL
          OR cs.Value != 'true'
      )";
            return _repository.GetSettings(sql);
        }


        /// <summary>
        /// 获取物流预警平台启动配置
        /// </summary>
        /// <returns></returns>
        public List<LogisticQueryEnableSettingModel> GetLogisticQueryEnableSetting()
        {
            // var key = LogisticQueryEnableSetting;
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToList<LogisticQueryEnableSettingModel>();
            // else
            // {
            //     var config = Get<List<LogisticQueryEnableSettingModel>>(key, 0);
            //     if (config == null || !config.Any())
            //     {
            //         config = new List<LogisticQueryEnableSettingModel>
            //         {
            //             new LogisticQueryEnableSettingModel {PlatformType="Pinduoduo",IsDefaultEnable=true,RuleType=1,Version="1,5" },
            //             new LogisticQueryEnableSettingModel {PlatformType="TouTiao",IsDefaultEnable=true,RuleType=1,Version="1,5" },
            //         };
            //         Set(key, config.ToJson(), 0);
            //     }
            //     HttpRuntime.Cache.Insert(key, config.ToJson(), null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            //     //var value = HttpRuntime.Cache[key].ExtToString().ToObject<List<string>>();
            //     return config;
            // }

            var key = LogisticQueryEnableSetting;
            var config = Get<List<LogisticQueryEnableSettingModel>>(key, 0);
            if (config == null || !config.Any())
            {
                config = new List<LogisticQueryEnableSettingModel>
                {
                    new LogisticQueryEnableSettingModel
                        { PlatformType = "Pinduoduo", IsDefaultEnable = true, RuleType = 1, Version = "1,5" },
                    new LogisticQueryEnableSettingModel
                        { PlatformType = "TouTiao", IsDefaultEnable = true, RuleType = 1, Version = "1,5" },
                };
                Set(key, config.ToJson(), 0);
            }
            return config;
        }

        public string GetLogisticUpdateStartTime()
        {
            // var key = $"/Cache/LogisticUpdateStartTime";
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToString();
            // else
            // {
            //     var config = Get(key, 0)?.Value;
            //     if (config.IsNullOrEmpty())
            //     {
            //         config = CustomerConfig.LogisticUpdateStartTime.IsNullOrEmpty() ? DateTime.Now.ToString("yyyy-MM-dd") : CustomerConfig.LogisticUpdateStartTime;
            //         Set(key, config, 0);
            //     }
            //     HttpRuntime.Cache.Insert(key, config, null, DateTime.Now.AddMinutes(30), System.Web.Caching.Cache.NoSlidingExpiration);
            //     //var value = HttpRuntime.Cache[key].ExtToString().ToObject<List<string>>();
            //     return config;
            // }

            const string key = "/Cache/LogisticUpdateStartTime";
            var config = Get(key, 0)?.Value;
            if (config.IsNullOrEmpty())
            {
                config = CustomerConfig.LogisticUpdateStartTime.IsNullOrEmpty()
                    ? DateTime.Now.ToString("yyyy-MM-dd")
                    : CustomerConfig.LogisticUpdateStartTime;
                Set(key, config, 0);
            }

            return config;
        }

        public int GetLogisticUpdateIntervalSeconds()
        {
            // var key = $"/Cache/LogisticUpdateIntervalSeconds";
            // var cache = HttpRuntime.Cache[key].ToInt();
            // if (cache > 0)
            //     return cache;
            // else
            // {
            //     var config = Get(key, 0)?.Value.ToInt() ?? 0;
            //     if (config == 0)
            //     {
            //         config = 300;
            //         Set(key, config.ToString(), 0);
            //     }
            //     HttpRuntime.Cache.Insert(key, config, null, DateTime.Now.AddMinutes(30), System.Web.Caching.Cache.NoSlidingExpiration);
            //     //var value = HttpRuntime.Cache[key].ExtToString().ToObject<List<string>>();
            //     return config;
            // }

            const string key = "/Cache/LogisticUpdateIntervalSeconds";
            var config = Get(key, 0)?.Value.ToInt() ?? 0;
            if (config == 0)
            {
                config = 300;
                Set(key, config.ToString(), 0);
            }
            return config;
        }

        /// <summary>
        /// 获取淘宝店铺默认的推送库
        /// </summary>
        /// <returns></returns>
        private TaobaoPushDbSetting GetTaobaoPushDbSetting(int shopId = -1, bool isUseDefaultConfig = true)
        {
            var defaultPushDbSetting = new TaobaoPushDbSetting
            { ConfigName = "TaobaoSynData", RdsName = "jrdsitvmm3nj", DbType = "SQLServer" };
            var value = Get($"/System/Config/Taobao/DefaultPushDbName", shopId)?.Value;
            if (string.IsNullOrEmpty(value))
            {
                return isUseDefaultConfig ? defaultPushDbSetting : null;
            }

            var s = value.ToObject<TaobaoPushDbSetting>();
            if (s == null || string.IsNullOrEmpty(s.RdsName) || string.IsNullOrEmpty(s.DbType) ||
                string.IsNullOrEmpty(s.ConfigName))
            {
                return isUseDefaultConfig ? defaultPushDbSetting : null;
            }
            return s;
        }

        /// <summary>
        /// 默认淘宝推送库
        /// </summary>
        /// <returns></returns>
        public TaobaoPushDbSetting GetDefaultTaobaoPushDb()
        {
            return new TaobaoPushDbSetting
            { ConfigName = "TaobaoSynData", RdsName = "jrdsitvmm3nj", DbType = "SQLServer" };
        }

        public TaobaoPushDbSetting GetTaobaoShopPushDbSetting(int shopId, bool isUseDefaultConfig = true)
        {
            return GetTaobaoPushDbSetting(shopId, isUseDefaultConfig);
        }

        /// <summary>
        /// 获取淘宝店铺的推送库配置，如果没有则使用默认的
        /// </summary>
        /// <returns></returns>
        public TaobaoPushDbSetting GetDefaultTaobaoPushDbSetting()
        {
            return GetTaobaoPushDbSetting(-1);
        }
        /// <summary>
        /// 创建淘宝推送库配置：已经有配置了，返回原始配置，若没有则创建
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public TaobaoPushDbSetting CreateTaobaoPushDbSetting(int shopId, bool isNewShop)
        {
            if (shopId <= 0)
                return null;
            var key = "/System/Config/Taobao/DefaultPushDbName";
            if (isNewShop)
            {
                var s = GetDefaultTaobaoPushDbSetting();
                Set(key, s.ToJson(), shopId);
                return s;
            }
            else
            {
                var value = Get(key, shopId)?.Value;
                //老店铺，先获取配置，获取不到返回空值，不能返回默认值
                if (string.IsNullOrEmpty(value) == false)
                    return value.ToObject<TaobaoPushDbSetting>();
                return null;
            }
        }

        /// <summary>
        /// 设置订单同步状态
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="syncStatus"></param>
        /// <param name="synCompleteTime"></param>
        /// <param name="lastSyncMessage"></param>
        /// <param name="isUpdateFullSyncStatus"></param>
        public void SetOrderSyncStatus(int shopId, ShopSyncStatusType syncStatus, DateTime? starTime = null, DateTime? synCompleteTime = null, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false)
        {
            SyncOrderParameterConfigModel syncOrderParameter = null;
            if (syncStatus == ShopSyncStatusType.Syncing)
            {
                //db.ExecuteWithNoSyncToOtherConfigDb("UPDATE P_Shop SET LastSyncStatus=@syncStatus,StartSyncTime=GetDate() WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds });
                syncOrderParameter = new SyncOrderParameterConfigModel()
                {
                    LastSyncStatus = syncStatus.ToString(),
                    StartSyncTime = DateTime.Now,
                    LastSyncTime = starTime,
                };
            }
            else
            {
                if (syncStatus == ShopSyncStatusType.Finished)
                {
                    if (synCompleteTime == null)
                        throw new ArgumentException("设置店铺为同步完成时，必须提供完成时间", "synCompleteTime");
                    else
                    {
                        //db.ExecuteWithNoSyncToOtherConfigDb($"UPDATE P_Shop SET LastSyncStatus=@syncStatus,LastSyncTime=@lastSyncTime,LastSyncMessage=@lastSyncMessage {(isUpdateFullSyncStatus ? ",FullSyncStatus='Complete'" : "")} WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncTime = synCompleteTime.Value, lastSyncMessage });
                        syncOrderParameter = new SyncOrderParameterConfigModel()
                        {
                            LastSyncStatus = syncStatus.ToString(),
                            LastSyncTime = synCompleteTime.Value,
                            LastSyncMessage = lastSyncMessage
                        };
                    }

                }
                else if (syncStatus == ShopSyncStatusType.Error)
                {
                    //db.ExecuteWithNoSyncToOtherConfigDb("UPDATE P_Shop SET LastSyncStatus=@syncStatus,LastSyncMessage=@lastSyncMessage WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncMessage });
                    syncOrderParameter = new SyncOrderParameterConfigModel()
                    {
                        LastSyncStatus = syncStatus.ToString(),
                        LastSyncMessage = lastSyncMessage,
                        LastSyncTime = starTime,
                    };
                }
            }
            Set(SyncOrderParamterConfigKey, syncOrderParameter.ToJson(), shopId);
        }


        /// <summary>
        /// 获取订单同步状态
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="syncStatus"></param>
        /// <param name="synCompleteTime"></param>
        /// <param name="lastSyncMessage"></param>
        /// <param name="isUpdateFullSyncStatus"></param>
        public SyncOrderParameterConfigModel GetOrderSyncStatus(int shopId)
        {
            var syncOrderParameter = Get<SyncOrderParameterConfigModel>(SyncOrderParamterConfigKey, shopId);
            return syncOrderParameter;
        }

        public DateTime GetLastMongoDbStasticTime(int interval = 1440)
        {
            var key = $"/System/Config/MongoDB/LastMongoDbStasticTime/{interval}";
            var version = CustomerConfig.MongoLogDBConnectionString.ToLower().Split('/', '?').LastOrDefault()?.TrimStart("log") ?? "";
            if (string.IsNullOrEmpty(version) == false && version.ToInt() > 0)
                key = $"/System/Config/MongoDB/LastMongoDbStasticTime/{version}/{interval}";
            var setting = Get(key, 0);
            var time = setting?.Value?.ToDateTime();
            if (time == null || time < DateTime.Now.AddMonths(-1))
            {
                setting = new CommonSetting
                {
                    Key = key,
                    Value = "",
                    ShopId = 0
                };
                if (interval == 1440)
                    setting.Value = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
                else
                    setting.Value = DateTime.Now.ToString("yyyy-MM-dd");
                return setting.Value.ToDateTime().Value;
            }
            else
                return time.Value;
        }

        public void SetLastMongoDbStasticTime(string value, int interval = 1440)
        {
            var key = $"/System/Config/MongoDB/LastMongoDbStasticTime/{interval}";
            var version = CustomerConfig.MongoLogDBConnectionString.ToLower().Split('/', '?').LastOrDefault()?.TrimStart("log") ?? "";
            if (string.IsNullOrEmpty(version) == false && version.ToInt() > 0)
                key = $"/System/Config/MongoDB/LastMongoDbStasticTime/{version}/{interval}";
            var setting = Get(key, 0);
            if (setting != null)
            {
                setting.Value = value;
                Update(setting);
            }
        }

        public DateTime GetLastRelationShopbCheckTime()
        {
            var key = $"/System/Config/Migrate/LastRelationShopbCheckTime";
            var setting = Get(key, 0);
            var time = setting?.Value?.ToDateTime();
            if (time == null || time < DateTime.Now.AddDays(-1))
            {
                setting = new CommonSetting
                {
                    Key = key,
                    Value = "",
                    ShopId = 0
                };
                setting.Value = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
                return setting.Value.ToDateTime().Value;
            }
            else
                return time.Value;
        }

        public void SetLastRelationShopbCheckTime(string value)
        {
            var key = $"/System/Config/Migrate/LastRelationShopbCheckTime";
            var setting = Get(key, 0);
            if (setting != null)
            {
                setting.Value = value;
                Update(setting);
            }
        }

        public bool GetIsWriteRefreshTokenLog()
        {
            // var key = $"/System/Fendan/Config/IsWriteRefreshTokenLog";
            // var cache = HttpRuntime.Cache[key];
            // if (cache != null)
            // {
            //     return cache.ToString() == "1";
            // }
            // else
            // {
            //     var setting = Get(key, 0);
            //     var isTrue = setting?.Value == "1" ? "1" : "0";
            //     HttpRuntime.Cache.Insert(key, isTrue, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return isTrue == "1";
            // }

            const string key = "/System/Fendan/Config/IsWriteRefreshTokenLog";
            var setting = Get(key, 0);
            var isTrue = setting?.Value == "1" ? "1" : "0";
            return isTrue == "1";
        }
        public DateTime GetLastTraceIncrimentTime(string pt, string excluedPt)
        {
            var key = $"/System/Config/Trace/LastTraceIncrimentTime";
            if (string.IsNullOrEmpty(pt) == false)
                key += $"/{pt}";
            if (string.IsNullOrEmpty(excluedPt) == false)
                key += $"/!{excluedPt}";
            var setting = Get(key, 0);
            var defaultTime = DateTime.Now.AddDays(-1);
            if (setting != null)
            {
                var time = setting.Value?.ToDateTime();
                if (time != null)
                    return time.Value;
            }
            return defaultTime;
        }

        public void SetLastTraceIncrimentTime(string value, string pt, string excluedPt)
        {
            var key = $"/System/Config/Trace/LastTraceIncrimentTime";
            if (string.IsNullOrEmpty(pt) == false)
                key += $"/{pt}";
            if (string.IsNullOrEmpty(excluedPt) == false)
                key += $"/!{excluedPt}";
            var setting = Get(key, 0);
            if (setting != null)
            {
                setting.Value = value;
                Update(setting);
            }
            else
                Add(new CommonSetting
                {
                    ShopId = 0,
                    Key = key,
                    Value = value
                });
        }

        public string GetUseNewPurchaseTemplateStartTime()
        {
            // var key = $"/Cache/Purchase/UseNewPurchaseTemplateStartTime";
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToString();
            // else
            // {
            //     var config = Get(key, 0)?.Value;
            //     if (config.IsNullOrEmpty())
            //     {
            //         config = DateTime.Now.ToString("yyyy-MM-dd");
            //         Set(key, config, 0);
            //     }
            //     HttpRuntime.Cache.Insert(key, config, null, DateTime.Now.AddMinutes(30), System.Web.Caching.Cache.NoSlidingExpiration);
            //     //var value = HttpRuntime.Cache[key].ExtToString().ToObject<List<string>>();
            //     return config;
            // }

            const string key = "/Cache/Purchase/UseNewPurchaseTemplateStartTime";
            var config = Get(key, 0)?.Value;
            if (config.IsNullOrEmpty())
            {
                config = DateTime.Now.ToString("yyyy-MM-dd");
                Set(key, config, 0);
            }
            return config;
        }

        public string GetDayPrintBatchNumber(int shopId)
        {
            var settingKeys = new List<string>() { dayPrintBatchNumberKey, dayPrintBatchDateKey };
            var settings = GetSets(settingKeys, shopId);
            var dayPrintBatchDate = settings.FirstOrDefault(f => f.Key == dayPrintBatchDateKey);
            var dayPrintBatchNumber = settings.FirstOrDefault(f => f.Key == dayPrintBatchNumberKey);
            if (dayPrintBatchDate?.Value != DateTime.Now.ToString("yyyyMMdd"))
            {
                return "0001";
            }
            else
            {
                var number = "";
                if (dayPrintBatchNumber != null)
                    number = (dayPrintBatchNumber.Value.ToInt() + 1).ToString();
                else
                    number = "1";
                return number.PadLeft(4, '0');
            }
        }

        //public void SetDayPrintBatchNumber(int shopId)
        //{
        //    var settingKeys = new List<string>() { dayPrintBatchNumberKey, dayPrintBatchDateKey };
        //    var settings = GetSets(settingKeys, shopId);
        //    var dayPrintBatchDate = settings.FirstOrDefault(f => f.Key == dayPrintBatchDateKey);
        //    var dayPrintBatchNumber = settings.FirstOrDefault(f => f.Key == dayPrintBatchNumberKey);

        //    var date = DateTime.Now.ToString("yyyyMMdd");
        //    if (dayPrintBatchDate == null || dayPrintBatchNumber == null)
        //    {
        //        if (dayPrintBatchDate == null)
        //        {
        //            _repository.Add(new CommonSetting()
        //            {
        //                Key = dayPrintBatchDateKey,
        //                Value = date,
        //                ShopId = shopId
        //            });
        //        }
        //        else if (dayPrintBatchDate.Value != date)
        //        {
        //            dayPrintBatchDate.Value = date;
        //            _repository.Update(dayPrintBatchDate);
        //        }

        //        if (dayPrintBatchNumber == null)
        //        {
        //            _repository.Add(new CommonSetting()
        //            {
        //                Key = dayPrintBatchNumberKey,
        //                Value = "1",
        //                ShopId = shopId
        //            });
        //        }
        //        else
        //        {
        //            dayPrintBatchNumber.Value = "1";
        //            _repository.Update(dayPrintBatchNumber);
        //        }
        //    }
        //    else
        //    {
        //        if (dayPrintBatchDate.Value != date)
        //        {
        //            dayPrintBatchDate.Value = date;
        //            _repository.Update(dayPrintBatchDate);

        //            dayPrintBatchNumber.Value = "1";
        //            _repository.Update(dayPrintBatchNumber);
        //        }
        //        else
        //        {
        //            _repository.UpdateDayPrintBatchNumber(dayPrintBatchNumberKey, shopId);
        //        }
        //    }
        //}

        /// <summary>
        /// 获取系统版本设置信息
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public static SystemVersionControlSettingModel GetSystemVersionControlSettings(string platformType)
        {
            // var key = "/System/Config/SystemVersionControlSettingModel/" + platformType;
            // var cache = HttpRuntime.Cache[key] as SystemVersionControlSettingModel;
            // if (cache != null)
            //     return cache;
            // var obj = new CommonSettingRepository().Get(key, 0)?.Value?.ToObject<SystemVersionControlSettingModel>();
            // if (obj == null)
            //     obj = new SystemVersionControlSettingModel();
            // HttpRuntime.Cache.Insert(key, obj, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            // return obj;

            var key = "/System/Config/SystemVersionControlSettingModel/" + platformType;
            var obj = new CommonSettingRepository().Get(key, 0)?.Value?.ToObject<SystemVersionControlSettingModel>() ??
                      new SystemVersionControlSettingModel();
            return obj;
        }

        /// <summary>
        /// 获取系统版本设置信息
        /// </summary>
        /// <returns></returns>
        public static bool IsDouDianPayApiUseProxy()
        {
            // var key = "/Fendan/Config/IsDouDianPayApiUseProxy";
            // var cache = HttpRuntime.Cache[key];
            // if (cache != null)
            //     return cache.ToString() == "1";
            // var value = new CommonSettingRepository().Get(key, 0)?.Value;
            // if (string.IsNullOrEmpty(value))
            //     value = "";
            // HttpRuntime.Cache.Insert(key, value, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            // return value == "1";

            const string key = "/Fendan/Config/IsDouDianPayApiUseProxy";
            var value = new CommonSettingRepository().Get(key, 0)?.Value;
            if (string.IsNullOrEmpty(value))
                value = "";
            return value == "1";
        }

        public static PlatformVersionTipInfo GetPlatformVersionTipInfoByShop(Shop shop, string controllerName, string actionName)
        {
            //判断版本权限：目前仅阻断Ajax操作、后期看情况处理
            PlatformVersionTipInfo model = null;
            var path = $"/{controllerName}/{actionName}";
            var systemVersion = shop.SystemVersion;
            if (string.IsNullOrEmpty(systemVersion))
                systemVersion = "V0";
            if (shop == null)
                return model;
            var setting = GetSystemVersionControlSettings(shop.PlatformType);
            if (setting == null)
                return model;

            var isAllowed = setting.CheckPermission(systemVersion, path, true);

            if (isAllowed == false)
            {
                var versionControl = setting?.MatchVersionControl(systemVersion);
                //找出支持改功能的版本
                var targetVersion = setting.VersionControls
                    ?.Where(y => y.AllowActions.Any(x => string.Equals(path, x, StringComparison.OrdinalIgnoreCase)))
                    ?.OrderBy(x => x.VersionRate)
                    ?.FirstOrDefault()?.SystemVersion;
                var targetVersionModel = setting.VersionMappings?.FirstOrDefault(x => x.SystemVersion == targetVersion);
                model = new PlatformVersionTipInfo
                {
                    CurrentSystemVersion = versionControl.SystemVersion,
                    CurrentSystemVersionName = setting.VersionMappings?.FirstOrDefault(x => x.SystemVersion == versionControl.SystemVersion)?.Name,
                    TargetVersion = targetVersionModel,
                    NeedUpgradeShopNames = new List<string> { shop.NickName },
                    PayLink = shop.PayUrl
                };
            }
            return model;
        }

        public static PlatformVersionTipInfo GetPlatformVersionTipInfoByShop(List<Shop> shops, string controllerName, string actionName)
        {
            //判断版本权限：目前仅阻断Ajax操作、后期看情况处理
            PlatformVersionTipInfo model = null;
            var path = $"/{controllerName}/{actionName}";
            foreach (var shop in shops)
            {
                var temp = GetPlatformVersionTipInfoByShop(shop, controllerName, actionName);
                if (temp != null)
                {
                    if (model == null)
                        model = temp;
                    else
                        model.NeedUpgradeShopNames.Add(shop.NickName);
                }

            }
            return model;
        }

        public static bool IsNeedShowSuggestUpgradeWindow(int shopId, string pageUrl)
        {
            return GetAlibabaSuggestUpgradeShopIds(pageUrl)?.Contains(shopId) == true;
        }

        /// <summary>
        /// 获取系统版本设置信息
        /// </summary>
        /// <returns></returns>
        public static AlibabaLogicsticsCpCodeType GetAlibabaLogicsticsCpCodeType()
        {
            // var key = "/System/Config/AlibabaLogicsticsCpCodeType";
            // var cache = HttpRuntime.Cache[key] as AlibabaLogicsticsCpCodeType;
            // if (cache != null)
            //     return cache;
            // var obj = new CommonSettingRepository().Get(key, 0)?.Value?.ToObject<AlibabaLogicsticsCpCodeType>();
            // if (obj == null)
            //     obj = new AlibabaLogicsticsCpCodeType();
            // HttpRuntime.Cache.Insert(key, obj, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            // return obj;

            const string key = "/System/Config/AlibabaLogicsticsCpCodeType";
            var obj = new CommonSettingRepository().Get(key, 0)?.Value?.ToObject<AlibabaLogicsticsCpCodeType>();
            if (obj == null)
                obj = new AlibabaLogicsticsCpCodeType();
            return obj;
        }

        /// 获取系统版本设置信息
        /// </summary>
        /// <returns></returns>
        public static List<int> GetAlibabaSuggestUpgradeShopIds(string pageUrl)
        {
            // var key = "/System/Config/AlibabaSuggestUpgradeShopIds/" + pageUrl?.Trim('/');
            // var cache = HttpRuntime.Cache[key] as List<int>;
            // if (cache != null)
            //     return cache;
            // var obj = new CommonSettingRepository().Get(key, 0)?.Value?.Split(',')?.Select(x => x.ToInt())?.ToList();
            // if (obj == null)
            //     obj = new List<int>();
            // HttpRuntime.Cache.Insert(key, obj, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            // return obj;

            var key = "/System/Config/AlibabaSuggestUpgradeShopIds/" + pageUrl?.Trim('/');
            var obj = new CommonSettingRepository().Get(key, 0)?.Value?.Split(',')?.Select(x => x.ToInt())?.ToList() ??
                      new List<int>();
            return obj;
        }

        public bool IsFxShopAutoMergerOpened(int systemShopId)
        {
            var mergerSetting = _repository.Get("/System/Config/FxUser/OrderMerger", systemShopId)?.Value;
            if (string.IsNullOrEmpty(mergerSetting) == false && mergerSetting?.ToInt() <= 0)
                return false;

            //var whiteList = Get("/System/Config/FenDan/AutoMerger/WhiteList", 0).Value?.Split(',').ToList();
            //var userMobile = SiteContext.Current.CurrentFxUser.Mobile;
            //if (whiteList.Contains(userMobile))
            //    return true;
            //else
            //    return false;

            return true;
        }

        /// <summary>
        /// 淘宝收件人加密灰度版本
        /// </summary>
        /// <returns></returns>
        public List<int> GetTaobaoDecryptVersion()
        {
            // var key = "/System/Taobao/ReceiverDecryptVersions";
            // var cache = HttpRuntime.Cache[key].ExtToString();
            // if (!string.IsNullOrEmpty(cache))
            //     return cache.ToObject<List<int>>();
            // else
            // {
            //     var config = Get<List<int>>(key, 0);
            //     if (config == null)
            //         config = new List<int>();
            //     HttpRuntime.Cache.Insert(key, config.ToJson(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return config;
            // }

            const string key = "/System/Taobao/ReceiverDecryptVersions";
            var config = Get<List<int>>(key, 0) ?? new List<int>();
            return config;
        }

        #region 乐观锁相关逻辑

        /// <summary>
        /// 尝试获取乐观锁，如果没有则创建
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <returns></returns>
        private OptimisticLock TryGetOptimisticLock(OptimisticLockOperationType op, string operationId)
        {
            var model = _repository.GetOptimisticLock(op, operationId);
            if (model == null)
            {
                model = _repository.CreateOptimisticLock(new OptimisticLock
                {
                    Operation = op,
                    OperationId = operationId,
                    IsLocked = false,
                    LastLockTime = DateTime.Now
                });
            }
            return model;
        }

        /// <summary>
        /// 尝试获乐观取锁
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <param name="maxLockMinutes"></param>
        /// <param name="sleepSeconds">睡眠时间，超过睡眠时间可以进入锁，否则进入失败。</param>
        /// <returns></returns>
        private bool TryAchieveOptimisticLock(OptimisticLockOperationType op, string operationId, int maxLockMinutes = 30, int sleepSeconds = 0)
        {
            if (maxLockMinutes <= 0)
                throw new ArgumentException("maxLockMinutes必须大于0");
            var model = TryGetOptimisticLock(op, operationId);
            //由于性能问题，针对某些操作可强制降低触发频率，第一次生成锁不触发睡眠
            if (!model.IsNewLock && sleepSeconds > 0)
            {
                if (model.LastLockTime > DateTime.Now.AddSeconds(-sleepSeconds))
                    return false;
            }
            //没有被锁定，直接锁定
            if (model.IsLocked == false)
                return _repository.TryAchieveOptimisticLock(op, operationId);

            //判断是否锁定超过设置时间，超过则强制获取锁
            if (model.LastLockTime < DateTime.Now.AddMinutes(-maxLockMinutes))
                return _repository.TryAchieveOptimisticLock(op, operationId, true);

            return false;
        }

        /// <summary>
        /// 尝试释放乐观锁
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <returns></returns>
        private bool TryReleaseOptimisticLock(OptimisticLockOperationType op, string operationId)
        {
            var success = _repository.TryReleaseOptimisticLock(op, operationId);
            if (success)
            {
                if (IsGlobalUseRedisOptimisticLockSwitch())
                {
                    SetOptimisticLockModeSwitch(op, operationId, 2);
                }
            }
            return success;
        }

        /// <summary>
        /// 设置乐观锁模式开关
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <param name="lockMode">1： SQL Server 乐观锁，2. Redis乐观锁， （默认值：1）</param>
        /// <returns></returns>
        public bool SetOptimisticLockModeSwitch(OptimisticLockOperationType op, string operationId, int lockMode)
        {
            var key = GetOptimisticLockModeKey(op);
            //return RedisHelper.HSet(key, operationId, lockMode);
            //按KEY，由于哈希表Redis集群无法负载，改用单独键存储配置
            var uniqueKey = $"{key}:{operationId}";
            return RedisHelper.Set(uniqueKey, lockMode);
        }

        /// <summary>
        /// 获取乐观锁模式键
        /// </summary>
        /// <param name="op"></param>
        /// <returns></returns>
        private string GetOptimisticLockModeKey(OptimisticLockOperationType op)
        {
            return CacheKeys.OptimisticLockModeSwitchKey
                .Replace("{CloudPlatformType}", CustomerConfig.CloudPlatformType)
                .Replace("{OptimisticLockOperationType}", op.ToString());
        }

        /// <summary>
        /// 获取乐观锁模式
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <returns></returns>
        public int GetOptimisticLockMode(OptimisticLockOperationType op, string operationId)
        {
            //是否完云切换Redis乐观锁
            var settingKey =
                SystemSettingKeys.IsAllCloudOpenRedisOptimisticLockKey.Replace("{CloudPlatformType}",
                    CustomerConfig.CloudPlatformType);
            var configValue = GetString(settingKey, 0);
            if (string.IsNullOrWhiteSpace(configValue) == false && configValue == "1")
            {
                return 2;
            }
            //锁键
            var key = GetOptimisticLockModeKey(op);
            //按KEY，由于哈希表Redis集群无法负载，改用单独键存储配置
            var uniqueKey = $"{key}:{operationId}";
            //如果单独键存在，则取单独键即可，否则取哈希
            var newLockMode = RedisHelper.Get<int?>(uniqueKey);
            if (newLockMode.HasValue)
            {
                return newLockMode.Value == 2 ? newLockMode.Value : 1;
            }
            //取哈希表配置
            var lockMode = RedisHelper.HGet<int>(key, operationId);
            var mode = lockMode == 2 ? lockMode : 1;
            //切换到独立键
            RedisHelper.Set(uniqueKey, mode);
            return mode;
        }

        /// <summary>
        /// 获取乐观锁状态
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <returns>true=已锁；false=未锁</returns>
        public bool GetOptimisticLock(OptimisticLockOperationType op, string operationId)
        {
            var result = false;
            var model = _repository.GetOptimisticLock(op, operationId);
            if (model != null && model.IsLocked)
                result = true;

            return result;
        }

        /// <summary>
        /// 在乐观锁中执行某个操作
        /// </summary>
        /// <typeparam name="TReturn"></typeparam>
        /// <param name="func"></param>
        /// <param name="action">未获取到锁的时候执行的动作</param>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <param name="maxLockMinutes"></param>
        /// <param name="sleepSeconds">睡眠时间，超过睡眠时间可以进入锁，否则进入失败。</param>
        /// <returns></returns>
        public ExcuteWithOptimisticLockResult<TReturn> ExecuteWithOptimisticLock<TReturn>(Func<TReturn> func,
            Action action, OptimisticLockOperationType op, string operationId, int maxLockMinutes = 30,
            int sleepSeconds = 0)
        {
            //是否使用Redis乐观锁
            if (IsGlobalUseRedisOptimisticLockSwitch() && GetOptimisticLockMode(op, operationId) == 2)
            {
                return ExecuteWithRedisOptimisticLock(func, action, op, operationId, maxLockMinutes, sleepSeconds);
            }
            //使用SQL Server乐观锁
            var hasAcheiveLock = false;
            var result = new ExcuteWithOptimisticLockResult<TReturn>()
            {
                IsExcuted = false,
                StartTime = DateTime.Now,
                Result = default(TReturn)
            };
            try
            {
                hasAcheiveLock = this.TryAchieveOptimisticLock(op, operationId, maxLockMinutes, sleepSeconds);
                if (hasAcheiveLock)
                {
                    result.IsExcuted = true;
                    result.Result = func();
                }
                else
                {
                    if (action != null)
                        action();
                }
            }
            catch (LogicException ex)
            {
                Log.WriteError($"在乐观锁中执行操作时发生错误：{ex}");
                result.ExceptionMessage = ex.Message;
                result.IsException = true;
                throw;
            }
            catch (Exception ex)
            {
                Log.WriteError($"在乐观锁中执行操作时发生错误：{ex}");
                result.ExceptionMessage = ex.ToString();
                result.IsException = true;
                throw;
            }
            finally
            {
                result.EndTime = DateTime.Now;
                if (hasAcheiveLock)
                {
                    var isReleased = this.TryReleaseOptimisticLock(op, operationId);
                    if (isReleased == false)
                        Log.WriteWarning($"释放乐观锁时发生错误");
                }
            }
            return result;
        }

        public ExcuteWithOptimisticLockResult<TReturn> ExecuteWithRedisOptimisticLock<TReturn>(Func<TReturn> func,
            Action action, OptimisticLockOperationType op, string operationId, int maxLockMinutes = 30,
            int sleepSeconds = 0)
        {
            //乐观锁键
            var key = GetOptimisticLockKey(op, operationId);
            //乐观锁值
            var lockValue = new OperationOptimisticLockValueModel
            {
                FxUserId = SiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0,
                Time = DateTime.Now
            };
            //乐观锁日志
            var logModel = new OptimisticLockLog
            {
                FxUserId = lockValue.FxUserId,
                OperationType = op.ToString(),
                OperationId = operationId,
                ExpireMinutes = maxLockMinutes
            };
            //乐观锁处理（获取锁重试 1 次）
            var handleResult =
                RedisLockHandler.OptimisticLock(key, lockValue, func, action, expireMinutes: maxLockMinutes,
                    getLockRetryTimes: 1, logModel: logModel,
                    writeLogAction: log => OptimisticLockDataEventTrackingService.Instance.WriteLog(log));
            //返回
            return new ExcuteWithOptimisticLockResult<TReturn>
            {
                IsExcuted = handleResult.IsGetLock,
                Result = handleResult.Result,
                StartTime = handleResult.StartTime,
                EndTime = handleResult.EndTime,
                IsException = handleResult.IsError,
                ExceptionMessage = handleResult.ErrorMessage
            };
        }

        /// <summary>
        /// 获取乐观锁键
        /// </summary>
        /// <param name="op"></param>
        /// <param name="operationId"></param>
        /// <returns></returns>
        private string GetOptimisticLockKey(OptimisticLockOperationType op, string operationId)
        {
            var key = CacheKeys.OptimisticLockKey.Replace("{CloudPlatformType}", CustomerConfig.CloudPlatformType)
                .Replace("{OptimisticLockOperationType}", op.ToString()).Replace("{OperationId}", operationId);
            return key;
        }

        public sealed class ExcuteWithOptimisticLockResult<TReturn>
        {
            public bool IsExcuted { get; set; }
            public TReturn Result { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            /// <summary>
            /// 是否发生了异常
            /// </summary>
            public bool IsException { get; set; }
            public string ExceptionMessage { get; set; }
        }


        #endregion

        public string GetFxDefaultSetting(string platformType, string key)
        {
            var ptKey = $"ErpWeb_Default_{platformType}_{key}";
            key = $"ErpWeb_Default_{key}";
            var keys = new List<string> { key, ptKey };
            var commSets = _repository.GetSets(keys, 0);
            var commonSet = commSets.FirstOrDefault(m => m.Key == ptKey);
            if (commonSet == null)
                commonSet = commSets.FirstOrDefault(m => m.Key == key);
            return commonSet?.Value.ToString2() ?? "";
        }

        public CommonSetting GetFxDefaultSetting(string platformType, string key, int sid)
        {
            var ptKey = $"/ErpWeb/Default/{platformType}/{key}";
            key = $"/ErpWeb/Default/{key}";
            var keys = new List<string> { key, ptKey };
            var commSets = _repository.GetSets(keys, sid);
            var commonSet = commSets.FirstOrDefault(m => m.Key == ptKey && m.ShopId == sid);
            if (commonSet == null)
                commonSet = commSets.FirstOrDefault(m => m.Key == key && m.ShopId == sid);
            if (commonSet == null)
            {
                commSets = _repository.GetSets(keys, 0);
                commonSet = commSets.FirstOrDefault(m => m.Key == ptKey);
                if (commonSet == null)
                    commonSet = commSets.FirstOrDefault(m => m.Key == key);
            }
            return commonSet;
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        public void DeleteByKey(string key, int shopId)
        {
            _repository.Delete(key, shopId);
        }
        public void DeleteByWx(string key, int userId)
        {
            _repository.DeleteRdsByWx(key, userId);
        }

        public string GetRdsByString(string key, int shopId, bool isDelete = false)
        {
            return _repository.GetRdsByString(key, shopId, isDelete);
        }

        public void AddRds(string key, string value)
        {
            _repository.AddRds(key, value);
        }






        #region 分库相关

        //如果只有一个分库,原样返回
        //如果有多个分库,按照分库分组商家(商家可在多个分库中出现)
        //然后重新赋值新的商家下拉框信息
        public string RecoverSupplierViewBag(int fxuserid, List<SupplierUser> agents, List<DbConfigModel> dbarea)
        {
            if (dbarea.Count > 1)
            {
                var supplierUserIds = agents.Select(x => x.FxUserId).ToList();

                var dbareadic = DbPolicyExtension.GetConfigFxSupplier(fxuserid, supplierUserIds);
                var dbbusines = DbPolicyExtension.GetConfigFxSuppliers(fxuserid, supplierUserIds, new List<DbConfigModel> { SiteContext.Current.CurrentDbConfig });
                List<int> suppliergroup = new List<int>();
                if (dbareadic.ContainsKey(SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName))
                {
                    suppliergroup.AddRange(dbareadic[SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName]);
                }
                if (dbbusines.ContainsKey(SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName) && dbbusines[SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName] != null)
                {
                    suppliergroup.AddRange(dbbusines[SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName]);
                }
                ;

                if (suppliergroup == null || !suppliergroup.Any())
                {
                    return null;
                }
                else
                {
                    suppliergroup = suppliergroup.Distinct().ToList();
                    var agentJson = agents?.Where(x => suppliergroup.Any(y => y == x.FxUserId)).Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.NickName, x.Remark, x.RemarkName, x.IsTop, x.IsShowShop }).Distinct().ToJson();
                    return agentJson.IsNullOrEmpty() ? null : agentJson;
                }
            }
            var agentJson2 = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.NickName, x.Remark, x.RemarkName, x.IsTop, x.IsShowShop }).Distinct().ToJson();
            return agentJson2.IsNullOrEmpty() ? null : agentJson2;
        }


        #endregion

        #region 分单用户是否开启付费模式

        /// <summary>
        /// 分单用户是否开启付费模式
        /// </summary>
        /// <returns></returns>
        public bool IsFxUserShouldPay(int fxUserId)
        {
            var key = $"/System/Config/IsFxUserShouldPay";
            var cacheKey = key + fxUserId;
            var cache = HttpRuntime.Cache[cacheKey];
            if (cache != null)
            {
                return cache.ToString() == "1";
            }
            else
            {
                var setting = Get(key, fxUserId);
                var isTrue = setting?.Value == "1" ? "1" : "0";
                HttpRuntime.Cache.Insert(cacheKey, isTrue, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
                return isTrue == "1";
            }
        }
        /// <summary>
        /// 头条快递列表数据
        /// </summary>
        /// <returns></returns>
        public List<ExpressCompanyCodeModel> GetTouTiaoExpressCompanys()
        {
            var key = "/System/TouTiao/ExpressCompanyCodes";
            var config = Get<List<ExpressCompanyCodeModel>>(key, 0);
            if (config == null)
            {
                var json = "[{\"code\":\"yzdsbk\",\"id\":1109,\"name\":\"邮政电商标快\"},{\"code\":\"shunfengguoji\",\"id\":1040,\"name\":\"顺丰国际\"},{\"code\":\"shunfengchengpei\",\"id\":1053,\"name\":\"顺丰城配\"},{\"code\":\"ganzhouanju\",\"id\":1077,\"name\":\"具语\"},{\"code\":\"fujianpupu\",\"id\":1099,\"name\":\"朴朴\"},{\"code\":\"linshiwuliu\",\"id\":1031,\"name\":\"林氏物流\"},{\"code\":\"zilegongmao\",\"id\":1038,\"name\":\"紫乐工贸\"},{\"code\":\"beijingbenlai\",\"id\":1060,\"name\":\"本来鲜\"},{\"code\":\"shenzhenshihai\",\"id\":1101,\"name\":\"海王星辰\"},{\"code\":\"huitongkuaidi\",\"id\":20,\"name\":\"百世快递(常用)\"},{\"code\":\"shunfeng\",\"id\":12,\"name\":\"顺丰速运(常用)\"},{\"code\":\"youzhengguonei\",\"id\":19,\"name\":\"邮政快递包裹\"},{\"code\":\"jingdongdajian\",\"id\":1079,\"name\":\"京东大件\"},{\"code\":\"shanghaiyibai\",\"id\":1067,\"name\":\"叮咚买菜\"},{\"code\":\"shanghaijinmi\",\"id\":1068,\"name\":\"侠刻送\"},{\"code\":\"savor\",\"id\":1025,\"name\":\"海信物流\"},{\"code\":\"jingdongkuaiyun\",\"id\":1080,\"name\":\"京东快运\"},{\"code\":\"guangdongjingguang\",\"id\":1094,\"name\":\"鲸广直运\"},{\"code\":\"xiaoshidayun\",\"id\":1098,\"name\":\"小时达测试物流商\"},{\"code\":\"dsukuaidi\",\"id\":1018,\"name\":\"D速快递\"},{\"code\":\"annto\",\"id\":861,\"name\":\"安得物流\"},{\"code\":\"xintianweng\",\"id\":1039,\"name\":\"信天翁同城速配\"},{\"code\":\"xlair\",\"id\":1051,\"name\":\"快弟来了\"},{\"code\":\"xianjiuru\",\"id\":1075,\"name\":\"鲜菜园\"},{\"code\":\"jinguangsudikuaijian\",\"id\":517,\"name\":\"京广速递\"},{\"code\":\"taijin\",\"id\":1032,\"name\":\"泰进物流\"},{\"code\":\"NZSY\",\"id\":1048,\"name\":\"哪吒速运\"},{\"code\":\"duodianshenzhen\",\"id\":1061,\"name\":\"多点配送\"},{\"code\":\"suyoda\",\"id\":1090,\"name\":\"速邮达\"},{\"code\":\"rrs\",\"id\":641,\"name\":\"日日顺物流\"},{\"code\":\"danniao\",\"id\":1017,\"name\":\"丹鸟\"},{\"code\":\"meiriyouxian\",\"id\":1044,\"name\":\"每日优鲜\"},{\"code\":\"mingchuangyoupinmcyp\",\"id\":1071,\"name\":\"名创优品\"},{\"code\":\"zhongyouex\",\"id\":1022,\"name\":\"众邮快递\"},{\"code\":\"udalogistic\",\"id\":1043,\"name\":\"韵达国际\"},{\"code\":\"smwfloor\",\"id\":1084,\"name\":\"湘粤华通\"},{\"code\":\"guangxijiaojiu\",\"id\":1092,\"name\":\"酒小二\"},{\"code\":\"DouyinExpress\",\"id\":1093,\"name\":\"官方代发\"},{\"code\":\"tiandihuayu\",\"id\":518,\"name\":\"天地华宇\"},{\"code\":\"shanhuodidi\",\"id\":1042,\"name\":\"闪货极速达\"},{\"code\":\"guangzhoubenxi\",\"id\":1063,\"name\":\"菜加壹（废弃）\"},{\"code\":\"dingdangkuaiyao\",\"id\":1085,\"name\":\"叮当快药\"},{\"code\":\"tiantian\",\"id\":14,\"name\":\"天天快递\"},{\"code\":\"shanghaiganlin\",\"id\":1076,\"name\":\"菜公社\"},{\"code\":\"shanghaixintian\",\"id\":1083,\"name\":\"信天翁小时达\"},{\"code\":\"zhongtiewuliu\",\"id\":610,\"name\":\"中铁飞豹\"},{\"code\":\"lntjs\",\"id\":289,\"name\":\"特急送\"},{\"code\":\"shanxijianhua\",\"id\":1037,\"name\":\"山西建华\"},{\"code\":\"zhongqingtingcai\",\"id\":1069,\"name\":\"听菜到家\"},{\"code\":\"jiayiwuliu\",\"id\":10,\"name\":\"佳怡物流\"},{\"code\":\"yijiuyijiu\",\"id\":1029,\"name\":\"壹玖壹玖\"},{\"code\":\"fengwang\",\"id\":1033,\"name\":\"丰网速运\"},{\"code\":\"chuanhua\",\"id\":1049,\"name\":\"传化物流\"},{\"code\":\"beijingmeiri\",\"id\":1054,\"name\":\"小时达测试\"},{\"code\":\"qingdaoriri\",\"id\":1089,\"name\":\"日日顺智慧物联\"},{\"code\":\"beijingdaguan\",\"id\":1062,\"name\":\"达冠\"},{\"code\":\"baishiwuliu\",\"id\":637,\"name\":\"百世快运\"},{\"code\":\"yundakuaiyun\",\"id\":952,\"name\":\"韵达快运\"},{\"code\":\"yuantongguoji\",\"id\":1034,\"name\":\"圆通国际\"},{\"code\":\"baiguoyuanind\",\"id\":1058,\"name\":\"百果园\"},{\"code\":\"zhimakaimen\",\"id\":440,\"name\":\"芝麻开门\"},{\"code\":\"debangwuliu\",\"id\":13,\"name\":\"德邦快递(常用)\"},{\"code\":\"henanjiubian\",\"id\":1088,\"name\":\"酒便利\"},{\"code\":\"zhongqingzhuandan\",\"id\":1102,\"name\":\"转单宝\"},{\"code\":\"subida\",\"id\":716,\"name\":\"速必达\"},{\"code\":\"zhihuashi\",\"id\":1045,\"name\":\"芝华仕物流\"},{\"code\":\"shenzhenshishun\",\"id\":1064,\"name\":\"顺丰同城\"},{\"code\":\"xianyouyi\",\"id\":1073,\"name\":\"萝卜青菜\"},{\"code\":\"exfresh\",\"id\":284,\"name\":\"安鲜达\"},{\"code\":\"nongfushanquan\",\"id\":1036,\"name\":\"农夫山泉物流\"},{\"code\":\"lyh\",\"id\":1050,\"name\":\"联运汇\"},{\"code\":\"ztocc\",\"id\":1052,\"name\":\"中通冷链\"},{\"code\":\"shenzhendinghai\",\"id\":1059,\"name\":\"花果鲜\"},{\"code\":\"gansushantong\",\"id\":1097,\"name\":\"兰马同城\"},{\"code\":\"debangkuaiyun\",\"id\":1100,\"name\":\"德邦快运\"},{\"code\":\"huangmajia\",\"id\":37,\"name\":\"黄马甲\"},{\"code\":\"pingandatengfei\",\"id\":565,\"name\":\"平安达腾飞快递\"},{\"code\":\"jtexpress\",\"id\":1021,\"name\":\"极兔速递\"},{\"code\":\"shunfengkuaiyun\",\"id\":1035,\"name\":\"顺丰快运\"},{\"code\":\"shenzhenshixing\",\"id\":1104,\"name\":\"幸福西饼\"},{\"code\":\"shentong\",\"id\":8,\"name\":\"申通快递(常用)\"},{\"code\":\"sxjdfreight\",\"id\":1024,\"name\":\"顺心捷达\"},{\"code\":\"chengdouruoxi\",\"id\":1066,\"name\":\"京西菜市\"},{\"code\":\"xuanchengshixun\",\"id\":1087,\"name\":\"寻梦\"},{\"code\":\"jiayunmeiwuliu\",\"id\":327,\"name\":\"加运美\"},{\"code\":\"zhongtongguoji\",\"id\":597,\"name\":\"中通国际\"},{\"code\":\"ztky\",\"id\":140,\"name\":\"中铁快运\"},{\"code\":\"xlobo\",\"id\":475,\"name\":\"贝海国际速递\"},{\"code\":\"zhongqingcailao\",\"id\":1056,\"name\":\"菜老包\"},{\"code\":\"jiangsuchuangji\",\"id\":1057,\"name\":\"果多美\"},{\"code\":\"yilongex\",\"id\":1082,\"name\":\"亿隆速运\"},{\"code\":\"kuayue\",\"id\":397,\"name\":\"跨越速运\"},{\"code\":\"jd\",\"id\":30,\"name\":\"京东物流(常用)\"},{\"code\":\"ems\",\"id\":17,\"name\":\"EMS(常用)\"},{\"code\":\"huadidianzishangwu\",\"id\":1055,\"name\":\"花递\"},{\"code\":\"suer\",\"id\":243,\"name\":\"速尔快递\"},{\"code\":\"yimidida\",\"id\":897,\"name\":\"壹米滴答\"},{\"code\":\"gujiajiaju\",\"id\":1030,\"name\":\"顾家家居\"},{\"code\":\"guangzhoubenxin\",\"id\":1070,\"name\":\"菜加壹\"},{\"code\":\"suning\",\"id\":202,\"name\":\"苏宁物流\"},{\"code\":\"stosolution\",\"id\":968,\"name\":\"申通国际\"},{\"code\":\"yunda\",\"id\":9,\"name\":\"韵达快递(常用)\"},{\"code\":\"fushanshilu\",\"id\":1091,\"name\":\"禄昌物流\"},{\"code\":\"jiuyescm\",\"id\":191,\"name\":\"九曳供应链\"},{\"code\":\"zhongtongkuaiyun\",\"id\":846,\"name\":\"中通快运\"},{\"code\":\"zhongtong\",\"id\":15,\"name\":\"中通快递(常用)\"},{\"code\":\"ndwl\",\"id\":317,\"name\":\"南方传媒物流\"},{\"code\":\"dajiangwangluo\",\"id\":1065,\"name\":\"达达\"},{\"code\":\"guangzhouzhixianzx\",\"id\":1072,\"name\":\"诚食生鲜\"},{\"code\":\"guangdongmeiyimy\",\"id\":1074,\"name\":\"美宜佳\"},{\"code\":\"yuantong\",\"id\":7,\"name\":\"圆通快递(常用)\"},{\"code\":\"youshuwuliu\",\"id\":11,\"name\":\"优速\"},{\"code\":\"annengwuliu\",\"id\":32,\"name\":\"安能物流\"},{\"code\":\"amusorder\",\"id\":177,\"name\":\"Amazon Logistics\"},{\"code\":\"shanghaixiake\",\"id\":1086,\"name\":\"同达\"},{\"code\":\"yueluwuliu\",\"id\":1096,\"name\":\"跃陆物流\"},{\"code\":\"anxl\",\"id\":631,\"name\":\"安迅物流\"},{\"code\":\"wanxiangwuliu\",\"id\":31,\"name\":\"万象物流\"},{\"code\":\"zhaijisong\",\"id\":21,\"name\":\"宅急送\"},{\"code\":\"sfwl\",\"id\":1026,\"name\":\"盛丰物流\"},{\"code\":\"shenghuiwuliu\",\"id\":244,\"name\":\"盛辉物流\"},{\"code\":\"XMTC\",\"id\":1047,\"name\":\"寻梦同城\"},{\"code\":\"beijingtengfu\",\"id\":1095,\"name\":\"牵牛花\"},{\"code\":\"yuantongkuaiyun\",\"id\":909,\"name\":\"圆通快运\"},{\"code\":\"xinfengwuliu\",\"id\":36,\"name\":\"信丰物流\"},{\"code\":\"jiangsujingchun\",\"id\":1103,\"name\":\"京东酒世界\"}]";
                config = json.ToList<ExpressCompanyCodeModel>();
                Set(key, json, 0);
            }
            return config;

            //var cache = HttpRuntime.Cache[key].ExtToString();
            //if (!string.IsNullOrEmpty(cache))
            //    return cache.ToObject<List<ExpressCompanyCodeModel>>();
            //else
            //{
            //    var config = Get<List<ExpressCompanyCodeModel>>(key, 0);
            //    if (config == null)
            //    {
            //        var json = "[{\"code\":\"shunfengguoji\",\"id\":1040,\"name\":\"顺丰国际\"},{\"code\":\"shunfengchengpei\",\"id\":1053,\"name\":\"顺丰城配\"},{\"code\":\"ganzhouanju\",\"id\":1077,\"name\":\"具语\"},{\"code\":\"fujianpupu\",\"id\":1099,\"name\":\"朴朴\"},{\"code\":\"linshiwuliu\",\"id\":1031,\"name\":\"林氏物流\"},{\"code\":\"zilegongmao\",\"id\":1038,\"name\":\"紫乐工贸\"},{\"code\":\"beijingbenlai\",\"id\":1060,\"name\":\"本来鲜\"},{\"code\":\"shenzhenshihai\",\"id\":1101,\"name\":\"海王星辰\"},{\"code\":\"huitongkuaidi\",\"id\":20,\"name\":\"百世快递(常用)\"},{\"code\":\"shunfeng\",\"id\":12,\"name\":\"顺丰速运(常用)\"},{\"code\":\"youzhengguonei\",\"id\":19,\"name\":\"邮政快递包裹\"},{\"code\":\"jingdongdajian\",\"id\":1079,\"name\":\"京东大件\"},{\"code\":\"shanghaiyibai\",\"id\":1067,\"name\":\"叮咚买菜\"},{\"code\":\"shanghaijinmi\",\"id\":1068,\"name\":\"侠刻送\"},{\"code\":\"savor\",\"id\":1025,\"name\":\"海信物流\"},{\"code\":\"jingdongkuaiyun\",\"id\":1080,\"name\":\"京东快运\"},{\"code\":\"guangdongjingguang\",\"id\":1094,\"name\":\"鲸广直运\"},{\"code\":\"xiaoshidayun\",\"id\":1098,\"name\":\"小时达测试物流商\"},{\"code\":\"dsukuaidi\",\"id\":1018,\"name\":\"D速快递\"},{\"code\":\"annto\",\"id\":861,\"name\":\"安得物流\"},{\"code\":\"xintianweng\",\"id\":1039,\"name\":\"信天翁同城速配\"},{\"code\":\"xlair\",\"id\":1051,\"name\":\"快弟来了\"},{\"code\":\"xianjiuru\",\"id\":1075,\"name\":\"鲜菜园\"},{\"code\":\"jinguangsudikuaijian\",\"id\":517,\"name\":\"京广速递\"},{\"code\":\"taijin\",\"id\":1032,\"name\":\"泰进物流\"},{\"code\":\"NZSY\",\"id\":1048,\"name\":\"哪吒速运\"},{\"code\":\"duodianshenzhen\",\"id\":1061,\"name\":\"多点配送\"},{\"code\":\"suyoda\",\"id\":1090,\"name\":\"速邮达\"},{\"code\":\"rrs\",\"id\":641,\"name\":\"日日顺物流\"},{\"code\":\"danniao\",\"id\":1017,\"name\":\"丹鸟\"},{\"code\":\"meiriyouxian\",\"id\":1044,\"name\":\"每日优鲜\"},{\"code\":\"mingchuangyoupinmcyp\",\"id\":1071,\"name\":\"名创优品\"},{\"code\":\"zhongyouex\",\"id\":1022,\"name\":\"众邮快递\"},{\"code\":\"udalogistic\",\"id\":1043,\"name\":\"韵达国际\"},{\"code\":\"smwfloor\",\"id\":1084,\"name\":\"湘粤华通\"},{\"code\":\"guangxijiaojiu\",\"id\":1092,\"name\":\"酒小二\"},{\"code\":\"DouyinExpress\",\"id\":1093,\"name\":\"官方代发\"},{\"code\":\"tiandihuayu\",\"id\":518,\"name\":\"天地华宇\"},{\"code\":\"shanhuodidi\",\"id\":1042,\"name\":\"闪货极速达\"},{\"code\":\"guangzhoubenxi\",\"id\":1063,\"name\":\"菜加壹（废弃）\"},{\"code\":\"dingdangkuaiyao\",\"id\":1085,\"name\":\"叮当快药\"},{\"code\":\"tiantian\",\"id\":14,\"name\":\"天天快递\"},{\"code\":\"shanghaiganlin\",\"id\":1076,\"name\":\"菜公社\"},{\"code\":\"shanghaixintian\",\"id\":1083,\"name\":\"信天翁小时达\"},{\"code\":\"zhongtiewuliu\",\"id\":610,\"name\":\"中铁飞豹\"},{\"code\":\"lntjs\",\"id\":289,\"name\":\"特急送\"},{\"code\":\"shanxijianhua\",\"id\":1037,\"name\":\"山西建华\"},{\"code\":\"zhongqingtingcai\",\"id\":1069,\"name\":\"听菜到家\"},{\"code\":\"jiayiwuliu\",\"id\":10,\"name\":\"佳怡物流\"},{\"code\":\"yijiuyijiu\",\"id\":1029,\"name\":\"壹玖壹玖\"},{\"code\":\"fengwang\",\"id\":1033,\"name\":\"丰网速运\"},{\"code\":\"chuanhua\",\"id\":1049,\"name\":\"传化物流\"},{\"code\":\"beijingmeiri\",\"id\":1054,\"name\":\"小时达测试\"},{\"code\":\"qingdaoriri\",\"id\":1089,\"name\":\"日日顺智慧物联\"},{\"code\":\"beijingdaguan\",\"id\":1062,\"name\":\"达冠\"},{\"code\":\"baishiwuliu\",\"id\":637,\"name\":\"百世快运\"},{\"code\":\"yundakuaiyun\",\"id\":952,\"name\":\"韵达快运\"},{\"code\":\"yuantongguoji\",\"id\":1034,\"name\":\"圆通国际\"},{\"code\":\"baiguoyuanind\",\"id\":1058,\"name\":\"百果园\"},{\"code\":\"zhimakaimen\",\"id\":440,\"name\":\"芝麻开门\"},{\"code\":\"debangwuliu\",\"id\":13,\"name\":\"德邦快递(常用)\"},{\"code\":\"henanjiubian\",\"id\":1088,\"name\":\"酒便利\"},{\"code\":\"zhongqingzhuandan\",\"id\":1102,\"name\":\"转单宝\"},{\"code\":\"subida\",\"id\":716,\"name\":\"速必达\"},{\"code\":\"zhihuashi\",\"id\":1045,\"name\":\"芝华仕物流\"},{\"code\":\"shenzhenshishun\",\"id\":1064,\"name\":\"顺丰同城\"},{\"code\":\"xianyouyi\",\"id\":1073,\"name\":\"萝卜青菜\"},{\"code\":\"exfresh\",\"id\":284,\"name\":\"安鲜达\"},{\"code\":\"nongfushanquan\",\"id\":1036,\"name\":\"农夫山泉物流\"},{\"code\":\"lyh\",\"id\":1050,\"name\":\"联运汇\"},{\"code\":\"ztocc\",\"id\":1052,\"name\":\"中通冷链\"},{\"code\":\"shenzhendinghai\",\"id\":1059,\"name\":\"花果鲜\"},{\"code\":\"gansushantong\",\"id\":1097,\"name\":\"兰马同城\"},{\"code\":\"debangkuaiyun\",\"id\":1100,\"name\":\"德邦快运\"},{\"code\":\"huangmajia\",\"id\":37,\"name\":\"黄马甲\"},{\"code\":\"pingandatengfei\",\"id\":565,\"name\":\"平安达腾飞快递\"},{\"code\":\"jtexpress\",\"id\":1021,\"name\":\"极兔速递\"},{\"code\":\"shunfengkuaiyun\",\"id\":1035,\"name\":\"顺丰快运\"},{\"code\":\"shenzhenshixing\",\"id\":1104,\"name\":\"幸福西饼\"},{\"code\":\"shentong\",\"id\":8,\"name\":\"申通快递(常用)\"},{\"code\":\"sxjdfreight\",\"id\":1024,\"name\":\"顺心捷达\"},{\"code\":\"chengdouruoxi\",\"id\":1066,\"name\":\"京西菜市\"},{\"code\":\"xuanchengshixun\",\"id\":1087,\"name\":\"寻梦\"},{\"code\":\"jiayunmeiwuliu\",\"id\":327,\"name\":\"加运美\"},{\"code\":\"zhongtongguoji\",\"id\":597,\"name\":\"中通国际\"},{\"code\":\"ztky\",\"id\":140,\"name\":\"中铁快运\"},{\"code\":\"xlobo\",\"id\":475,\"name\":\"贝海国际速递\"},{\"code\":\"zhongqingcailao\",\"id\":1056,\"name\":\"菜老包\"},{\"code\":\"jiangsuchuangji\",\"id\":1057,\"name\":\"果多美\"},{\"code\":\"yilongex\",\"id\":1082,\"name\":\"亿隆速运\"},{\"code\":\"kuayue\",\"id\":397,\"name\":\"跨越速运\"},{\"code\":\"jd\",\"id\":30,\"name\":\"京东物流(常用)\"},{\"code\":\"ems\",\"id\":17,\"name\":\"EMS(常用)\"},{\"code\":\"huadidianzishangwu\",\"id\":1055,\"name\":\"花递\"},{\"code\":\"suer\",\"id\":243,\"name\":\"速尔快递\"},{\"code\":\"yimidida\",\"id\":897,\"name\":\"壹米滴答\"},{\"code\":\"gujiajiaju\",\"id\":1030,\"name\":\"顾家家居\"},{\"code\":\"guangzhoubenxin\",\"id\":1070,\"name\":\"菜加壹\"},{\"code\":\"suning\",\"id\":202,\"name\":\"苏宁物流\"},{\"code\":\"stosolution\",\"id\":968,\"name\":\"申通国际\"},{\"code\":\"yunda\",\"id\":9,\"name\":\"韵达快递(常用)\"},{\"code\":\"fushanshilu\",\"id\":1091,\"name\":\"禄昌物流\"},{\"code\":\"jiuyescm\",\"id\":191,\"name\":\"九曳供应链\"},{\"code\":\"zhongtongkuaiyun\",\"id\":846,\"name\":\"中通快运\"},{\"code\":\"zhongtong\",\"id\":15,\"name\":\"中通快递(常用)\"},{\"code\":\"ndwl\",\"id\":317,\"name\":\"南方传媒物流\"},{\"code\":\"dajiangwangluo\",\"id\":1065,\"name\":\"达达\"},{\"code\":\"guangzhouzhixianzx\",\"id\":1072,\"name\":\"诚食生鲜\"},{\"code\":\"guangdongmeiyimy\",\"id\":1074,\"name\":\"美宜佳\"},{\"code\":\"yuantong\",\"id\":7,\"name\":\"圆通快递(常用)\"},{\"code\":\"youshuwuliu\",\"id\":11,\"name\":\"优速\"},{\"code\":\"annengwuliu\",\"id\":32,\"name\":\"安能物流\"},{\"code\":\"amusorder\",\"id\":177,\"name\":\"Amazon Logistics\"},{\"code\":\"shanghaixiake\",\"id\":1086,\"name\":\"同达\"},{\"code\":\"yueluwuliu\",\"id\":1096,\"name\":\"跃陆物流\"},{\"code\":\"anxl\",\"id\":631,\"name\":\"安迅物流\"},{\"code\":\"wanxiangwuliu\",\"id\":31,\"name\":\"万象物流\"},{\"code\":\"zhaijisong\",\"id\":21,\"name\":\"宅急送\"},{\"code\":\"sfwl\",\"id\":1026,\"name\":\"盛丰物流\"},{\"code\":\"shenghuiwuliu\",\"id\":244,\"name\":\"盛辉物流\"},{\"code\":\"XMTC\",\"id\":1047,\"name\":\"寻梦同城\"},{\"code\":\"beijingtengfu\",\"id\":1095,\"name\":\"牵牛花\"},{\"code\":\"yuantongkuaiyun\",\"id\":909,\"name\":\"圆通快运\"},{\"code\":\"xinfengwuliu\",\"id\":36,\"name\":\"信丰物流\"},{\"code\":\"jiangsujingchun\",\"id\":1103,\"name\":\"京东酒世界\"}]";
            //        config = json.ToList<ExpressCompanyCodeModel>();
            //        Set(key, json, 0);
            //    }
            //    HttpRuntime.Cache.Insert(key, config.ToJson(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //    return config;
            //}
        }


        #endregion

        /// <summary>
        /// 获取异步补偿的数据库Id
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public int GetAsyncDeliveryDbNameId(string cloudPlatformType)
        {
            // var key = $"/System/Config/$CloudPlatformType/BackUpSendhistoryDbId";
            // key = key.Replace("$CloudPlatformType", cloudPlatformType);
            //
            // var cache = HttpRuntime.Cache[key].ToInt();
            // if (cache > 0)
            //     return cache;
            // else
            // {
            //     var dbNameId = Get(key, 0)?.Value.ToInt() ?? 0;
            //     HttpRuntime.Cache.Insert(key, dbNameId, null, DateTime.Now.AddMinutes(30), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return dbNameId;
            // }

            var key = $"/System/Config/$CloudPlatformType/BackUpSendhistoryDbId";
            key = key.Replace("$CloudPlatformType", cloudPlatformType);
            var dbNameId = Get(key, 0, secondCacheExpireMinutes: 30)?.Value.ToInt() ?? 0;
            return dbNameId;
        }

        /// <summary>
        /// 是否开启全量加密，快手
        /// </summary>
        /// <returns></returns>
        public bool GetIsAllEncryptionByKuaiShouFx()
        {
            // var key = $"/System/Config/KuaiShouByFxEncryption/IsAll";
            // var cache = HttpRuntime.Cache[key];
            // if (cache != null)
            // {
            //     return cache.ToString() == "1";
            // }
            // else
            // {
            //     var setting = Get(key, 0);
            //     var isTrue = setting?.Value == "1" ? "1" : "0";
            //     HttpRuntime.Cache.Insert(key, isTrue, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return isTrue == "1";
            // }

            const string key = "/System/Config/KuaiShouByFxEncryption/IsAll";
            var setting = Get(key, 0);
            var isTrue = setting?.Value == "1" ? "1" : "0";
            return isTrue == "1";
        }

        public bool IsBucketToken()
        {
            // string key = "/System/Config/FenDan/IsBucketeToken";
            // var cache = HttpRuntime.Cache[key];
            // if (cache != null)
            // {
            //     return cache.ToString() == "1";
            // }
            // else
            // {
            //     var setting = Get(key, 0);
            //     var isTrue = setting?.Value == "1" ? "1" : "0";
            //     HttpRuntime.Cache.Insert(key, isTrue, null, DateTime.Now.AddMinutes(5), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return isTrue == "1";
            // }

            const string key = "/System/Config/FenDan/IsBucketeToken";
            var setting = Get(key, 0);
            var isTrue = setting?.Value == "1" ? "1" : "0";
            return isTrue == "1";
        }

        /// <summary>
        /// 获取配置数据（默认有缓存）
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <param name="runtime"></param>
        /// <returns></returns>
        public CommonSetting GetSetting(string key, int shopId, bool runtime = true)
        {
            if (key.IsNullOrEmpty())
                return new CommonSetting();

            if (!runtime)
            {
                return Get(key, shopId);
            }
            else
            {
                // var cache = HttpRuntime.Cache[key];
                // if (cache != null)
                //     return cache as CommonSetting;
                // else
                // {
                //     var config = Get(key, 0);
                //     if (config == null)
                //         config = new CommonSetting();
                //     HttpRuntime.Cache.Insert(key, config, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
                //     return config;
                // }

                var config = Get(key, 0, secondCacheExpireMinutes: 15) ?? new CommonSetting();
                return config;
            }
        }


        /// <summary>
        /// 是否是老用户
        /// 判定某个时间点前的为老用户,时间点后的为新用户
        /// </summary>
        /// <returns></returns>
        public bool IsTipsDouYinNewApp()
        {
            var result = "Old";
            string key = "/System/Config/FenDan/IsDouYinNewAppTime";
            CommonSetting setting = null;
            if (CustomerConfig.IsDebug)
            {
                setting = _repository.GetBySysCacheDebug(key, 0, 1);
            }
            else
            {
                setting = Get(key, 0);
            }
            result = setting?.Value;
            if (string.IsNullOrEmpty(result)) return true;
            DateTime dt;
            if (!DateTime.TryParse(result, out dt)) return true;
            if (SiteContext.Current.CurrentFxUser.CreateTime > dt)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 获取系统维护时间，增加二级缓存
        /// </summary>
        /// <returns></returns>
        public DateTime GetMaintenanceTime()
        {
            //返回失败默认返回这个
            const string result = "2024-12-11 06:00:00";
            const string key = SystemSettingKeys.SystemMaintenanceTime;
            try
            {
                var setting = _repository.GetBySysCache(key, 0, 1);
                if (setting != null && !string.IsNullOrEmpty(setting.Value))
                {
                    return setting.Value.toDateTime();
                }

                return result.toDateTime();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取系统维护时间失败，错误信息：{ex}");
                return result.toDateTime();
            }
        }


        /// <summary>
        /// 是否应用抖音open_address_id
        /// </summary>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public int FxSystemIsUseDyOpenAddressId(int systemShopId)
        {
            var key = CustomerConfig.FxSystemIsUseDyOpenAddressIdKey;
            return Get(key, systemShopId)?.Value.ToInt() ?? 0;
        }

        /// <summary>
        /// 是否显示1688菜单
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public bool IsShow1688Menu(int fxUserId)
        {
            var qingService = new QingService(fxUserId);
            //1.是否被开启过预付
            if (QingService.Instance.IsOpenedPrePay == true)
            {
                return true;
            }

            ////2.是否有1688店铺
            ///移除 2024.02.22
            //if (QingService.Instance.IsOpen1688Shops == true)
            //{
            //    return true;
            //}

            //3.是否开通了轻应用
            if (QingService.Instance.IsOpenQing == true)
            {
                return true;
            }
            //4.厂家白名单
            var supplierService = new SupplierUserService();
            if (supplierService.IsSupplierInWhitelist(fxUserId))
            {
                switch (QingService.Instance.UserRole)
                {
                    case QingUserRole.Busines:
                        break;
                    case QingUserRole.Factory:
                        QingService.Instance.UserRole = QingUserRole.Both;
                        break;
                    case QingUserRole.Both:
                        break;
                    case QingUserRole.None:
                        QingService.Instance.UserRole = QingUserRole.Busines;
                        break;
                    default:
                        QingService.Instance.UserRole = QingUserRole.Busines;
                        break;
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 是否显示1688菜单
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public bool GetIsShow1688Menu(int fxUserId, int systemShopId, bool isUseCache = true)
        {
            var config = FxCaching.GetConfig(FxCachingType.Show1688Menu);
            string rediskey = string.Format(config.Key, fxUserId);
            bool? isShow = null;
            if (config.IsOpenRead == true)
            {
                isShow = RedisCacheUtls.Get<bool?>(rediskey);
            }
            // 需要还原todo
            //isShow = false;
            //只缓存为开通的，未开通的变化比较大，暂时不处理
            if (isShow == null || isShow.Value == false)
            {
                isShow = IsShow1688Menu(fxUserId);
                if (isShow == true && config.IsOpenWrite == true)
                {
                    RedisCacheUtls.Set(rediskey, isShow, config.Expire);
                }
            }

            return isShow.Value;
            //var isShow = false;
            FxCaching.GetCache(FxCachingType.Show1688Menu, fxUserId.ToString(), () => { return IsShow1688Menu(fxUserId); });

            //bool result;
            //bool redisIsInit = RedisPool.IsInit;
            //isUseCache = isUseCache && redisIsInit;
            //var key = $"/FxSystem/IsShow1688Menu/{fxUserId}";



            ////保存到Redis，过期时间为3天
            //var seconds = 60 * 60 * 24 * 3;
            //seconds += new Random().Next(0, 7200);

            //if (isUseCache)
            //{
            //    var isExist = false;
            //    if (RedisHelper.Exists(key))
            //    {
            //        isExist = true;
            //        result = RedisHelper.Get(key).ToObject<bool>();
            //    }
            //    else
            //    {
            //        isExist = false;
            //        result = IsShow1688Menu(fxUserId);
            //    }

            //    if (isExist == false)
            //    {
            //        //未开通，过期时间为1小时
            //        if (result == false)
            //            seconds = 60 * 60;

            //        RedisHelper.Set(key, result, seconds);
            //    }

            //    return result;
            //}

            //result = IsShow1688Menu(fxUserId);
            //if (redisIsInit)
            //{
            //    //未开通，过期时间为1小时
            //    if (result == false)
            //        seconds = 60 * 60;

            //    RedisHelper.Set(key, result, seconds);
            //}

            //return result;
        }

        ///// <summary>
        ///// 更新是否显示1688菜单的缓存
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <param name="systemShopId"></param>
        //public void UpdateIsShow1688MenuCache(int fxUserId, int systemShopId = 0)
        //{
        //    try
        //    {
        //        FxCaching.ForeRefeshCache(FxCachingType.Show1688Menu, fxUserId.ToString2());
        //if (systemShopId <= 0)
        //    systemShopId = new FxUserShopService().GetFxUserIdMapping(new List<int> { fxUserId })?.FirstOrDefault()?.ShopId ?? 0;

        //bool redisIsInit = RedisPool.IsInit;
        //if (redisIsInit == false)
        //    return;

        //var key = $"/FxSystem/IsShow1688Menu/{fxUserId}";
        //if (RedisHelper.Exists(key))
        //{
        //    var result = RedisHelper.Get(key).ToObject<bool>();
        //    //已开通，直接返回
        //    if (result)
        //        return;
        //}

        //GetIsShow1688Menu(fxUserId, systemShopId, false);
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"UpdateIsShow1688MenuCache时异常：{ex}");
        //    }
        //}

        /// <summary>
        /// 是否关闭同步合并订单开关
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsDisabledSyncMergerOrder(int fxUserId)
        {
            //全局开关
            const string key = SystemSettingKeys.IsDisabledSyncMergerOrder;
            var globalCommonSetting = Get(key, 0);
            var isGlobalDisabledSyncMergerOrder = (globalCommonSetting?.Value ?? "0") == "1";
            if (isGlobalDisabledSyncMergerOrder)
            {
                Log.Debug(() => $"是否关闭同步合单（{fxUserId}），全局控制关闭同步合单。",
                    $"SyncMergerOrderSwitch_{DateTime.Now:yyyy-MM-dd}.log");
                return true;
            }

            //按白名单开关控制
            var commonSettings = Get(key, fxUserId);
            Log.Debug(() => $"是否关闭同步合单（{fxUserId}），白名单开关信息：{commonSettings.ToJson(true)}。",
                $"SyncMergerOrderSwitch_{DateTime.Now:yyyy-MM-dd}.log");
            return (commonSettings?.Value ?? "0") == "1";
        }
        /// <summary>
        /// 是否开启商品选择器V2版
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsEnabledProductSelectorSearchV2(int fxUserId)
        {
            //全局开关
            const string key = SystemSettingKeys.IsEnabledProductSelectorSearchV2;
            var globalCommonSetting = Get(key, 0);
            var isGlobalDisabledSyncMergerOrder = (globalCommonSetting?.Value ?? "0") == "1";
            if (isGlobalDisabledSyncMergerOrder)
            {
                Log.Debug($"是否开启商品选择查询V2版（{fxUserId}），全局控制关闭。", $"ProductSelectorSearchV2Switch_{DateTime.Now:yyyy-MM-dd}.log");
                return true;
            }
            //按白名单开关控制
            var commonSettings = Get(key, fxUserId);
            Log.Debug($"是否开启商品选择查询V2版（{fxUserId}），白名单开关信息：{commonSettings.ToJson(true)}。",
                $"ProductSelectorSearchV2Switch_{DateTime.Now:yyyy-MM-dd}.log");
            return (commonSettings?.Value ?? "0") == "1";
        }


        /// <summary>
        /// 是否开启对账查询V2版
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledSettlementBillQueryV2()
        {
            const string key = SystemSettingKeys.IsEnabledSettlementBillQueryV2;
            var commonSetting = Get(key, 0);
            var isEnabledSettlementBillQueryV2 = (commonSetting?.Value ?? "0") == "1";
            return isEnabledSettlementBillQueryV2;
        }

        public void SetCainiaoVirtualExpress(List<VirtualExpressModel> list)
        {
            //汇总虚拟快递信息存入配置表，用于发货时使用
            bool needUpdateSetting = false; //是否需要更新配置
            var key = "/ErpWeb/CaiNiao/VirtualExpress";
            var virtualExpressList = Get<List<VirtualExpressModel>>(key, -1);//ShopId改为-1，才能更新成功
            if (virtualExpressList == null || !virtualExpressList.Any())
            {
                needUpdateSetting = true;
                virtualExpressList = list;
            }
            else
            {
                foreach (var item in list)
                {
                    if (virtualExpressList.Any(f => f.ExpressId == item.ExpressId && f.CpCode == item.CpCode))
                        continue;
                    virtualExpressList.Add(item);
                    needUpdateSetting = true;
                }
            }
            if (needUpdateSetting)
            {
                Set(key, virtualExpressList.ToJson(), 0);
            }
        }

        /// <summary>
        /// 判断发货快递是否是虚拟快递
        /// </summary>
        /// <param name="expressId"></param>
        /// <returns></returns>
        public bool IsVirtualExpresss(int expressId)
        {
            var key = "/ErpWeb/CaiNiao/VirtualExpress";
            var virtualExpressList = Get<List<VirtualExpressModel>>(key, -1);
            if (virtualExpressList == null || !virtualExpressList.Any())
                return false;
            return virtualExpressList.Any(f => f.ExpressId == expressId);
        }
        /// <summary>
        /// 获取复制副本开关配置
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        public DuplicationSwitchConfigModel GetDuplicationSwitchConfig(string cloudPlatformType = null)
        {
            //转入云平台为空，则取当前云平台
            if (string.IsNullOrWhiteSpace(cloudPlatformType))
            {
                cloudPlatformType = CustomerConfig.CloudPlatformType;
            }
            //配置键
            var key = SystemSettingKeys.DuplicationSwitchSetKey.Replace("{CloudPlatformType}", cloudPlatformType);
            //获取配置值
            var value = _repository.GetValue(key, 0);
            //未配置
            if (string.IsNullOrWhiteSpace(value))
            {
                Log.WriteWarning("复制副本开关配置，未配置，命中默认的配置，请联系系统管理员配置。");
                return new DuplicationSwitchConfigModel();
            }
            //返回
            return value.ToObject<DuplicationSwitchConfigModel>();
        }

        public CommonSetting UnbindSupplier1688Shop(int systemShopId)
        {
            return _repository.UnbindSupplier1688Shop(systemShopId);
        }

        /// <summary>
        /// 是否开启发货消息
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledOnlineSendMessage()
        {
            //全局开关
            const string key = SystemSettingKeys.IsEnabledOnlineSendMessage;
            var commonSetting = Get(key, 0);
            //默认值处理
            var defaultValue = "1";
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                defaultValue = "0";
                return false;
            }
            //返回
            var isEnabledOnlineSendMessage = (commonSetting?.Value ?? defaultValue) == "1";
            if (!isEnabledOnlineSendMessage)
            {
                Log.WriteWarning("是否开启发货消息开关是关闭状态，不能进行发货消息收集，请联系系统管理员配置。",
                    $"CommonSetting_{DateTime.Now:yyyy-MM-dd}.log");
            }
            return isEnabledOnlineSendMessage;
        }

        /// <summary>
        /// 最大拆单遍历次数（按云平台配置）
        /// </summary>
        /// <returns></returns>
        public int MaxSplitOrderLoopCount()
        {
            //键
            var key = SystemSettingKeys.MaxSplitOrderLoopCountKey.Replace("{CloudPlatformType}",
                CustomerConfig.CloudPlatformType);
            //默认值
            const string defaultValue = "20";
            //配置
            var commonSetting = Get(key, 0);
            //配置值
            var value = (commonSetting?.Value ?? defaultValue).ToInt(20);
            //少于20，则默认20.
            if (value < 20)
            {
                value = 20;
            }
            //返回值
            return value;
        }

        private static readonly object Locker = new object();

        /// <summary>
        ///  从缓存中最大拆单遍历次数（按云平台配置）
        /// </summary>
        /// <returns></returns>
        public int MaxSplitOrderLoopCountWithCache()
        {
            // //键
            // var key = SystemSettingKeys.MaxSplitOrderLoopCountKey.Replace("{CloudPlatformType}",
            //     CustomerConfig.CloudPlatformType);
            //
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = 20;
            //     try
            //     {
            //         value = MaxSplitOrderLoopCount();
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取最大拆单遍历次数配置异常，配置值：{value}，异常原因：{e}",
            //             $"MaxSplitOrderLoopCountWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = 20;
            //     }
            //
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(10).TotalSeconds);
            // }
            //
            // var cacheValue = 20;
            // try
            // {
            //     cacheValue = CacheHelper.Get<int>(key);
            //     if (cacheValue < 20)
            //     {
            //         cacheValue = 20;
            //     }
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"从缓存获取最大拆单遍历次数配置异常，配置值：{cacheValue}，异常原因：{e}",
            //         $"MaxSplitOrderLoopCountWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = 20;
            // }
            //
            // return cacheValue;

            var value = 20;
            try
            {
                value = MaxSplitOrderLoopCount();
            }
            catch (Exception e)
            {
                Log.WriteError($"获取最大拆单遍历次数配置异常，配置值：{value}，异常原因：{e}",
                    $"MaxSplitOrderLoopCountWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                value = 20;
            }

            return value;
        }

        /// <summary>
        /// 抖店面单是否开启面单校验新逻辑，默认false
        /// </summary>
        /// <returns></returns>
        public bool IsNewTouTiaoLogisticService()
        {
            // var key = "/System/Config/Fendan/IsNewTouTiaoLogisticService";
            // var cache = HttpRuntime.Cache[key];
            // if (cache != null)
            // {
            //     return cache.ToString() == "1";
            // }
            // else
            // {
            //     var setting = Get(key, 0);
            //     var isTrue = setting?.Value == "1" ? "1" : "0";
            //     HttpRuntime.Cache.Insert(key, isTrue, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            //     return isTrue == "1";
            // }

            const string key = "/System/Config/Fendan/IsNewTouTiaoLogisticService";
            var setting = Get(key, 0, secondCacheExpireMinutes: 15);
            var isTrue = setting?.Value == "1" ? "1" : "0";
            return isTrue == "1";
        }

        /// <summary>
        /// 拼多多是否使用打单系统应用
        /// </summary>
        /// <returns></returns>
        public bool PddIsUsePrintSystemApp()
        {
            var key = "/FxSystem/Pinduoduo/IsUsePrintSystemApp";
            var v = Get(key, 0)?.Value ?? "";
            var result = v == "1" || v == "true";
            return result;
        }
        /// <summary>
        /// 拼多多是否使用打单系统应用[电子面单]
        /// </summary>
        /// <returns></returns>
        public bool PddIsUsePrintSystemAppForBill()
        {
            var key = "/FxSystem/Pinduoduo/IsUsePrintSystemAppForBill";
            var v = Get(key, 0)?.Value ?? "";
            var result = v == "1" || v == "true";
            return result;
        }

        #region 利润统计导出

        /// <summary>
        /// 获取头条利润统计导出字段配置
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, string> GetProfitStatisticsExportFields()
        {
            var key = "/SystemSetting/TouTiao/ProfitStatisticsExportFields";
            var v = Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(v))
                throw new LogicException("找不到导出字段配置");
            return v.ToObject<Dictionary<string, string>>();
        }

        /// <summary>
        /// 获取利润报表导出字段配置
        /// </summary>
        /// <param name="groupType"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public Dictionary<string, string> GetProfitReportExportFields(string groupType)
        {
            var key = "/SystemSetting/TouTiao/ProfitReportExportFields_" + groupType;
            var v = Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(v))
                throw new LogicException("找不到导出字段配置");
            return v.ToObject<Dictionary<string, string>>();
        }

        #endregion


        /// <summary>
        /// 是否开启你新的保存历史
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledUseNewSaveSendHistory(int fxUserId)
        {
            //开关键
            const string key = SystemSettingKeys.EnabledUseNewSaveSendHistorySwitchKey;
            var value = Get<SystemSettingValueModel>(key, 0);
            //默认，不开启
            if (value == null)
            {
                return false;
            }
            //全局开启
            if (value.IsGlobalUse)
            {
                return true;
            }
            //未配置用户，则未开启
            if (value.FxUserIds == null || !value.FxUserIds.Any())
            {
                return false;
            }

            //存在用户配置
            return value.FxUserIds.Contains(fxUserId);
        }



        /// <summary>
        /// 是否附加LoopJoin提示 库存业务
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public string AppendLoopJoinTipByWarehouseStock(int fxUserId)
        {
            return _repository.AppendLoopJoinTipByWarehouseStock(fxUserId);
        }

        /// <summary>
        /// 从缓存重获取SQL使用表值函数参数量
        /// </summary>
        /// <returns></returns>
        public int SqlUseTableFunParamCountWithCache()
        {
            return _repository.SqlUseTableFunParamCountWithCache();
        }



        /// <summary>
        /// 根据账号判断是否开启跨境功能
        /// </summary>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public bool IsShowCrossBorder(int fxUserId)
        {
            // 系统配置满足全局配置开启原则
            var globalCrossBorderEnabled = _repository.Get(CacheKeys.CrossBorderGlobalEnabledKey, 0)?.ToBool() ?? false;
            if (globalCrossBorderEnabled)
                return true;
            //获取用户标识信息
            var _userFlag = fxUserId != 0 ? new UserFxRepository().Get(fxUserId).UserFlag.ToString2() : string.Empty;
            //满足用户存在跨境标识
            return _userFlag != null && _userFlag.Contains(UserFxRepository.CrossBorderUserFlag);
        }

        /// <summary>
        /// 当前账号是否开启跨境功能
        /// </summary>
        /// <returns></returns>
        public bool IsShowCrossBorder()
        {
            return SiteContext.Current.IsShowCrossBorder;
        }

        /// <summary>
        /// 获取阿里云配置
        /// </summary>
        /// <param name="key"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public CommonSetting GetAlibaba(string key, int shopId)
        {
            return _repository.GetAlibaba(key, shopId);
        }

        /// <summary>
        /// 开启跨境的功能
        /// </summary>
        /// <param name="systemShopIds"></param>
        /// <returns></returns>
        public void EnabledCrossBorder(List<int> systemShopIds)
        {
            if (systemShopIds == null || !systemShopIds.Any())
                return;

            //开启跨境功能的用户白名单
            var key = CacheKeys.CrossBorderEnabledBySysSidKey;

            var lockKey = "Set_CrossBorderEnabledBySysSidKey_Lock";
            if (RedisHelper.Set(lockKey, "1", 5, CSRedis.RedisExistence.Nx)) //是否获取到锁
            {
                try
                {
                    var idList = systemShopIds;
                    //shopid -1
                    var crossBorderEnabledBySysSidSetting = Get(key, -1);
                    if (crossBorderEnabledBySysSidSetting == null || string.IsNullOrEmpty(crossBorderEnabledBySysSidSetting.Value))
                    {
                        crossBorderEnabledBySysSidSetting = new CommonSetting()
                        {
                            Key = key,
                            ShopId = -1,
                        };
                    }
                    else
                    {
                        var idStr = crossBorderEnabledBySysSidSetting.Value;
                        var ids = idStr.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).Select(f => f.ToInt()).ToList();
                        if (ids.Any())
                            idList.AddRange(ids);
                    }
                    idList = idList.Where(f => f > 0).Distinct().ToList();
                    if (idList.Any())
                    {
                        var val = $",{string.Join(",", idList.OrderBy(f => f))},";
                        //if (crossBorderEnabledBySysSidSetting != null && crossBorderEnabledBySysSidSetting.Id > 0)
                        //    Delete(crossBorderEnabledBySysSidSetting.Id);
                        Set(key, val, -1);
                        crossBorderEnabledBySysSidSetting.Value = val;

                        ////写入站点缓存(15分钟固定过期)
                        //HttpRuntime.Cache.Insert(key, crossBorderEnabledBySysSidSetting ?? new CommonSetting(), null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError(ex.ToString());
                }
                finally
                {
                    RedisHelper.Del(lockKey);
                }
            }
        }

        /// <summary>
        /// 获取消息提醒配置
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public ReminderSettingModel GetReminderSetting(int shopId)
        {
            return Get<ReminderSettingModel>(ReminderSetting, shopId);
        }

        /// <summary>
        /// 保存消息提醒配置
        /// </summary>
        /// <param name="model"></param>
        /// <param name="shopId"></param>
        public void SaveReminderSetting(ReminderSettingModel model, int shopId)
        {
            Set(ReminderSetting, model.ToJson(), shopId);
        }

        /// <summary>
        /// 获取包裹详情-全面单共用
        /// </summary>
        /// <param name="model">取号请求模型</param>
        /// <param name="productName">商品名称shortTitle/productSubject</param>
        /// <returns></returns>
        public static string GetPackageContent(GetWaybillCodeModel model, string productName = "")
        {
            var packageContent = string.Empty;
            try
            {
                Log.Debug("获取包裹详情-全面单共用，数据:" + model.ToJson(), "getpackagecontenterror.txt");

                if (model.ExpressSustenance != null && !string.IsNullOrWhiteSpace(model.ExpressSustenance.Name))
                {
                    packageContent = model.ExpressSustenance.Name;
                    if (model.ExpressSustenance.Name == "自定义")
                    {
                        packageContent = model.ExpressSustenance.Desc;
                    }
                }
                else
                {
                    //查数据库
                    if (model.PrintTemplate != null && model.PrintTemplate.Id > 0)
                    {
                        var packageInfo = new Services.TemplatePackageInfoService().GetByTemplateIdWithCache(model.PrintTemplate.Id);
                        if (packageInfo != null)
                        {
                            return packageInfo.PackageContent;
                        }
                    }

                    packageContent = string.IsNullOrWhiteSpace(productName) ? "物品" : CommUtls.TrimEmoji(productName);
                }

            }
            catch (Exception ex)
            {
                packageContent = "物品";
            }
            return packageContent;

        }

        /// <summary>
        /// 获取支持一单多包的快递公司编码
        /// </summary>
        /// <param name="templateType">模板类型，如CaiNiao</param>
        /// <returns></returns>
        public List<string> GetMultiPackageExpressCpCodes(string templateType)
        {
            var settingKey = $"/System/FenDan/{templateType}/MultiPackageExpress";
            try
            {
                var setting = Get(settingKey, 0);
                if (setting == null || string.IsNullOrEmpty(setting.Value))
                {
                    return new List<string>();
                }

                var result = setting.Value;
                return result.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
            }
            catch (Exception e)
            {
                Log.WriteError($"获取多包裹快递公司编码配置异常，配置键：{settingKey}，异常原因：{e}",
                    $"GetMultiPackageExpressCpCodesError_{DateTime.Now:yyyy-MM-dd}.txt");
                return new List<string>();
            }
        }


        /// <summary>
        /// 是否全局使用Redis乐观锁开关
        /// </summary>
        /// <returns></returns>
        public bool IsGlobalUseRedisOptimisticLockSwitch()
        {
            return _repository.IsGlobalUseRedisOptimisticLockSwitch();
        }
        /// <summary>
        /// 是否订单审核需要添加变更日志
        /// </summary>
        /// <returns></returns>
        public bool IsOrderCheckNeedAddChangeLog()
        {
            //键
            const string key = SystemSettingKeys.IsOrderCheckNeedAddChangeLogKey;
            //值
            var value = _repository.GetValue(key, 0);
            if (string.IsNullOrWhiteSpace(value))
            {
                return false;
            }
            //返回
            return value == "1";
        }

        /// <summary>
        /// 是否订单标签需要添加变更日志
        /// </summary>
        /// <returns></returns>
        public bool IsOrderTagNeedAddChangeLog()
        {
            //键
            const string key = SystemSettingKeys.IsOrderTagNeedAddChangeLogKey;
            //值
            var value = _repository.GetValue(key, 0);
            if (string.IsNullOrWhiteSpace(value))
            {
                return false;
            }
            //返回
            return value == "1";
        }
        /// <summary>
        /// 是否审核订单更新旧逻辑单信息需要记录变更日志
        /// </summary>
        /// <returns></returns>
        public bool IsUpdateOldOrderNeedAddChangeLog()
        {
            //键
            const string key = SystemSettingKeys.IsUpdateOldOrderNeedAddChangeLogKey;
            //值
            var value = _repository.GetValue(key, 0);
            if (string.IsNullOrWhiteSpace(value))
            {
                return false;
            }
            //返回
            return value == "1";
        }
        /// <summary>
        /// 是否开启复制副本锁卡开关
        /// </summary>
        /// <returns></returns>
        public bool IsCloseDataDuplicationLockSwitch(int fxUserId)
        {
            //配置
            var value = _repository.GetIsEnableDataDuplicationLockValueWithCache();
            if (string.IsNullOrWhiteSpace(value))
            {
                return false;
            }
            //值
            var configValue = value.ToObject<DataDuplicationLockSwitchConfigModel>();
            if (configValue == null)
            {
                return false;
            }

            if (configValue.GlobalSwitch)
            {
                return true;
            }

            if (configValue.AssignFxUserIds == null || !configValue.AssignFxUserIds.Any())
            {
                return false;
            }

            if (configValue.AssignFxUserIds.Contains(fxUserId))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 是否使用新的订单导出方法
        /// </summary>
        /// <returns></returns>
        public bool IsUseNewOrderExportMethod()
        {
            try
            {
                if (RedisPool.IsInit)
                    return RedisHelper.Get<bool>(CacheKeys.UseNewOrderExportMethodKey);
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        /// <summary>
        /// 支持采集数据链路类型
        /// </summary>
        /// <returns></returns>
        public List<string> SupportTraceDataTypes()
        {
            //开关键
            const string key = SystemSettingKeys.SupportTraceDataTypes;
            var value = Get<List<string>>(key, 0);
            //默认，不开启
            if (value == null || value.Any() == false)
            {
                return new List<string> { "InvokeOrderApi" };
            }
            return value;
        }
        /// <summary>
        /// 支持采集数据链路类型（缓存）
        /// </summary>
        /// <returns></returns>
        public List<string> SupportTraceDataTypesWithCache()
        {
            //默认值
            var defaultValue = new List<string> { "InvokeOrderApi" };
            // //键
            // var key = SystemSettingKeys.SupportTraceDataTypes;
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = defaultValue;
            //     try
            //     {
            //         value = SupportTraceDataTypes();
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取支持采集数据链路类型配置异常，配置值：{value?.ToJson(true)}，异常原因：{e}",
            //             $"SupportTraceDataTypesWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = defaultValue;
            //     }
            //
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(10).TotalSeconds);
            // }
            //
            // var cacheValue = defaultValue;
            // try
            // {
            //     cacheValue = CacheHelper.Get<List<string>>(key);
            //     if (cacheValue == null || cacheValue.Any() == false)
            //     {
            //         cacheValue = defaultValue;
            //     }
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"从缓存获取支持采集数据链路类型配置异常，配置值：{cacheValue?.ToJson(true)}，异常原因：{e}",
            //         $"SupportTraceDataTypesWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = defaultValue;
            // }
            //
            // return cacheValue;

            var value = defaultValue;
            try
            {
                value = SupportTraceDataTypes();
            }
            catch (Exception e)
            {
                Log.WriteError($"获取支持采集数据链路类型配置异常，配置值：{value?.ToJson(true)}，异常原因：{e}",
                    $"SupportTraceDataTypesWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                value = defaultValue;
            }

            return value;
        }


        public bool IsEnableApiSimpleLog()
        {
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = defaultValue;
            //     try
            //     {
            //         var dbValue = Get<string>(key, 0);
            //         //默认，开启
            //         if (dbValue.IsNotNullOrEmpty())
            //         {
            //             value = dbValue == "1";
            //         }
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取简化日志配置开关错误，原因：{e}", $"IsEnableApiSimpleLog_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = defaultValue;
            //     }
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(10).TotalSeconds);
            // }
            //
            // var cacheValue = defaultValue;
            // try
            // {
            //     if (CacheHelper.Contains(key))
            //     {
            //         cacheValue = CacheHelper.Get<bool>(key);
            //     }
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"获取缓存简化日志配置开关错误，原因：{e}", $"IsEnableApiSimpleLog_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = defaultValue;
            // }
            // return cacheValue;


            var value = true;
            try
            {
                //键
                const string key = SystemSettingKeys.EnableApiSimpleLogKey;
                var dbValue = Get<string>(key, 0);
                //默认，开启
                if (dbValue.IsNotNullOrEmpty())
                {
                    value = dbValue == "1";
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"获取简化日志配置开关错误，原因：{e}", $"IsEnableApiSimpleLog_{DateTime.Now:yyyy-MM-dd}.log");
                value = true;
            }

            return value;
        }

        /// <summary>
        /// 获取订单同步分析补偿开关配置
        /// </summary>
        /// <returns></returns>
        private int GetOrderSyncAnalysisCompensateSwitchConfig(string platformType)
        {
            //键
            var key = SystemSettingKeys.OrderSyncAnalysisCompensateSwitchKey.Replace("{PlatformType}", platformType);
            //配置
            var commonSetting = Get(key, 0);
            //值
            var value = commonSetting?.Value ?? "0";
            return value.ToInt(0);
        }

        /// <summary>
        /// 是否开启订单同步补偿开关配置
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="notSyncOrderCount"></param>
        /// <returns></returns>
        public bool IsEnabledOrderSyncAnalysisCompensateWithCache(string platformType, int notSyncOrderCount)
        {
            //判空处理
            if (string.IsNullOrWhiteSpace(platformType) || notSyncOrderCount == 0)
            {
                return false;
            }
            // //键
            // var key = SystemSettingKeys.OrderSyncAnalysisCompensateSwitchKey.Replace("{PlatformType}", platformType);
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = 0;
            //     try
            //     {
            //         value = GetOrderSyncAnalysisCompensateSwitchConfig(platformType);
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取最大拆单遍历次数配置异常，配置值：{value}，异常原因：{e}",
            //             $"IsEnabledOrderSyncAnalysisCompensateWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = 0;
            //     }
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(1).TotalSeconds);
            // }
            //
            // var cacheValue = 0;
            // try
            // {
            //     cacheValue = CacheHelper.Get<int>(key);
            //     if (cacheValue < 0)
            //     {
            //         cacheValue = 0;
            //     }
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"从缓存获取最大拆单遍历次数配置异常，配置值：{cacheValue}，异常原因：{e}",
            //         $"IsEnabledOrderSyncAnalysisCompensateWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = 0;
            // }


            var value = 0;
            try
            {
                value = GetOrderSyncAnalysisCompensateSwitchConfig(platformType);
            }
            catch (Exception e)
            {
                Log.WriteError($"获取最大拆单遍历次数配置异常，配置值：{value}，异常原因：{e}",
                    $"IsEnabledOrderSyncAnalysisCompensateWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                value = 0;
            }

            //等于零，则关闭补偿
            if (value == 0)
            {
                return false;
            }
            //不同步订单数>=配置数
            return Math.Abs(notSyncOrderCount) >= value;
        }

        /// <summary>
        /// 订单分析补偿是否更新最后同步更新时间
        /// </summary>
        /// <returns></returns>
        private int GetOrderSyncAnalysisCompensateIsUpdateLastSyncTimeConfig()
        {
            //键
            var key = SystemSettingKeys.OrderSyncAnalysisCompensateIsUpdateLastSyncTimeKey;
            //配置
            var commonSetting = Get(key, 0);
            //值
            var value = commonSetting?.Value ?? "0";
            return value.ToInt(0);
        }

        /// <summary>
        /// 订单同步分析补偿是否更新最后同步时间
        /// </summary>
        /// <returns></returns>
        public bool OrderSyncAnalysisCompensateIsUpdateLastSyncTimeWithCache()
        {
            // //键
            // var key = SystemSettingKeys.OrderSyncAnalysisCompensateIsUpdateLastSyncTimeKey;
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = 0;
            //     try
            //     {
            //         value = GetOrderSyncAnalysisCompensateIsUpdateLastSyncTimeConfig();
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取订单同步分析是否更新最后同步时间配置异常，配置值：{value}，异常原因：{e}",
            //             $"OrderSyncAnalysisCompensateIsUpdateLastSyncTimeWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = 0;
            //     }
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(1).TotalSeconds);
            // }
            //
            // var cacheValue = 0;
            // try
            // {
            //     cacheValue = CacheHelper.Get<int>(key);
            //     if (cacheValue < 0)
            //     {
            //         cacheValue = 0;
            //     }
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"从缓存获取订单同步分析是否更新最后同步时间配置异常，配置值：{cacheValue}，异常原因：{e}",
            //         $"OrderSyncAnalysisCompensateIsUpdateLastSyncTimeWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = 0;
            // }

            var value = 0;
            try
            {
                value = GetOrderSyncAnalysisCompensateIsUpdateLastSyncTimeConfig();
            }
            catch (Exception e)
            {
                Log.WriteError($"获取订单同步分析是否更新最后同步时间配置异常，配置值：{value}，异常原因：{e}",
                    $"OrderSyncAnalysisCompensateIsUpdateLastSyncTimeWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                value = 0;
            }
            //等于零，则关闭补偿
            if (value == 0)
            {
                return false;
            }

            return value == 1;
        }

        /// <summary>
        /// 仅开启灰度冷数据副本（默认值：开启）
        /// </summary>
        /// <returns></returns>
        private int GetOnlyOpenGrayColdDataDuplicationConfig()
        {
            //键
            const string key = SystemSettingKeys.OnlyOpenGrayColdDataDuplicationKey;
            //配置
            var commonSetting = Get(key, 0);
            //值
            var value = commonSetting?.Value ?? "1";
            return value.ToInt(1);
        }

        /// <summary>
        /// 仅开启灰度冷数据副本（默认值：开启）
        /// </summary>
        /// <returns></returns>
        public bool IsOnlyOpenGrayColdDataDuplicationWithCache()
        {
            // //键
            // const string key = SystemSettingKeys.OnlyOpenGrayColdDataDuplicationKey;
            // if (!CacheHelper.Contains(key))
            // {
            //     var value = 1;
            //     try
            //     {
            //         value = GetOnlyOpenGrayColdDataDuplicationConfig();
            //     }
            //     catch (Exception e)
            //     {
            //         Log.WriteError($"获取仅开启灰度冷数据副本配置异常，配置值：{value}，异常原因：{e}",
            //             $"IsOnlyOpenGrayColdDataDuplicationWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //         value = 1;
            //     }
            //     CacheHelper.Set(key, value, TimeSpan.FromMinutes(5).TotalSeconds);
            // }
            //
            // var cacheValue = 1;
            // try
            // {
            //     cacheValue = CacheHelper.Get<int>(key);
            // }
            // catch (Exception e)
            // {
            //     Log.WriteError($"从缓存获取仅开启灰度冷数据副本配置异常，配置值：{cacheValue}，异常原因：{e}",
            //         $"IsOnlyOpenGrayColdDataDuplicationWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
            //     cacheValue = 1;
            // }


            var value = 1;
            try
            {
                value = GetOnlyOpenGrayColdDataDuplicationConfig();
            }
            catch (Exception e)
            {
                Log.WriteError($"获取仅开启灰度冷数据副本配置异常，配置值：{value}，异常原因：{e}",
                    $"IsOnlyOpenGrayColdDataDuplicationWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                value = 1;
            }
            return value == 1;
        }

        /// <summary>
        /// 获取多单号发货配置
        /// </summary>
        /// <returns>（是否启用多单号回传(0：先发单个单号，再弹窗发多运单号，1：全部使用新单号发货，空：每次弹窗确认)，各平台对多单号回传的配置）</returns>
        public (int?, ManyCodeSendConfigModel) GetManyCodeSendConfig()
        {
            //先查询是否允许多单号回传
            var key = "/ErpWeb/SetInfo/ManyCodeSendAway";
            var shopId = SiteContext.Current.CurrentShopId;
            var setting = GetString(key, shopId);
            //未开启多单号回传
            if (setting.IsNullOrEmpty())
                return (null, null);
            if (setting == "1")
                return (1, null);
            //继续查询各平台对多单号回传的配置
            key = "/ErpWeb/SetInfo/ManyCodeSendConfig";
            setting = GetString(key, shopId);
            //默认配置
            var config = new ManyCodeSendConfigModel();
            if (setting.IsNotNullOrEmpty())
            {
                config = setting.ToObject<ManyCodeSendConfigModel>();
                if (config == null)
                    config = new ManyCodeSendConfigModel();
            }
            return (0, config);
        }

        /// <summary>
        /// 获取外部通知设置
        /// 对接wms用的
        /// </summary>
        /// <returns></returns>
        public Data.Model.OpenPlatform.OutNoticeSettingModel GetOutNoticeSetting()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var key = "/FenFa/System/Config/OutNoticeSetting";
            var json = Get(key, shopId)?.Value ?? "";
            if (string.IsNullOrWhiteSpace(json))
                return null;
            return json.ToObject<Data.Model.OpenPlatform.OutNoticeSettingModel>();
        }

        /// <summary>
        /// 根据用户标识判断用户是否在白名单中（包含全局的判断）
        /// </summary>
        /// <param name="userFlag"></param>
        /// <returns></returns>
        public bool IsWhiteUserByProfitStatistics(string userFlag)
        {
            var isProfitStatisticsWhiteUser = userFlag != null && userFlag.Contains(UserFxRepository.ProfitStatisticsUserFlag);
            return IsWhiteUserByKey("/FxSystem/ProfitStatistics/WhiteUser", isProfitStatisticsWhiteUser);
        }

        /// <summary>
        /// 利润统计控制白名单
        /// </summary>
        /// <returns></returns>
        public bool IsWhiteUserByProfitStatistics()
        {
            //--利润统计全局控制白名单
            //--1 - 全部打开
            //--2 - 全部关闭
            //--3 - 指定用户打开（默认）
            return IsWhiteUserByKey("/FxSystem/ProfitStatistics/WhiteUser", SiteContext.Current.IsProfitStatisticsWhiteUser);
        }

        /// <summary>
        /// 短视频发布控制白名单
        /// </summary>
        /// <returns></returns>
        public bool IsShopVideoWhiteUser()
        {
            //--短视频发布全局控制白名单
            //--1 - 全部打开
            //--2 - 全部关闭
            //--3 - 指定用户打开（默认）
            return IsWhiteUserByKey("/FxSystem/ShopVideo/WhiteUser", SiteContext.Current.IsShopVideoWhiteUser);
        }

        /// <summary>
        /// 站内信控制白名单
        /// </summary>
        /// <returns></returns>
        public bool IsWhiteUserBySiteMessage()
        {
            //--站内信全局控制白名单
            //--1 - 全部打开
            //--2 - 全部关闭
            //--3 - 指定用户打开（默认）
            return IsWhiteUserByKey("/FxSystem/SiteMessage/WhiteUser", SiteContext.Current.IsSiteMessageUserFlagWhiteUser);
        }

        /// <summary>
        /// 对账中心大客版本，控制白名单
        /// </summary>
        /// <returns></returns>
        public bool IsWhiteUserByIsReconVip()
        {
            //--[对账中心大客版本]全局控制白名单
            //--1 - 全部打开
            //--2 - 全部关闭
            //--3 - 指定用户打开（默认）
            return IsWhiteUserByKey("/FxSystem/ReconVip/WhiteUser", SiteContext.Current.IsReconVip);
        }

        /// <summary>
        /// 通过key 判断用户是否在白名单中
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        private bool IsWhiteUserByKey(string key, bool isWhiteUser)
        {
            //全局控制白名单
            //--1 - 全部打开
            //--2 - 全部关闭
            //--3 - 指定用户打开（默认）
            var value = GetString(key, 0);
            if (value == null || value == "3")
            {
                return isWhiteUser;
            }
            return value == "1";
        }



        /// <summary>
        /// 获取OpenTelemetry指定埋点的接口
        /// </summary>
        /// <returns></returns>
        public List<string> GetOpenTelemetryFilterApiList()
        {
            List<string> configList = new List<string>();

            try
            {
                var cacheKey = "OpenTelemetryFilterApi";

                //读取接口,先查内存缓存
                // var cache = HttpRuntime.Cache[cacheKey] as List<string>;
                // if (cache != null)
                // {
                //     return cache;
                // }

                //内存缓存没有数据，查redis
                var commonSetting = Get(cacheKey, 0, secondCacheExpireMinutes: 10);
                if (commonSetting == null)
                {
                    return configList;
                }

                //解析
                configList = JsonConvert.DeserializeObject<List<string>>(commonSetting.Value);
                if (configList == null || configList.Count == 0)
                {
                    return configList;
                }

                //HttpRuntime.Cache.Insert(cacheKey, commonSetting.Value, null, DateTime.Now.AddMinutes(10), System.Web.Caching.Cache.NoSlidingExpiration);
            }
            catch (Exception e)
            {
                Log.WriteError($"获取OpenTelemetry指定埋点的接口，错误信息：{e}");
            }
            return configList;
        }

        /// <summary>
        /// 获取前端链路的配置信息
        /// </summary>
        /// <returns></returns>
        public OpenTelemetryWebConfigModel GetOpenTelemetryWebConfig()
        {
            OpenTelemetryWebConfigModel configModel = null;
            try
            {
                //查内存缓存
                var cacheKey = $"/ErpWeb/OpenTelemetryConfig/{CustomerConfig.CloudPlatformType}/{CustomerConfig.SystemEnvironmentVersion}";

                // configModel = HttpRuntime.Cache[cacheKey] as OpenTelemetryWebConfigModel;
                //
                // if (configModel == null)
                // {
                //     //系统配置
                //     var commonSettingModel = Get(cacheKey, 0);
                //
                //     if (commonSettingModel != null && !string.IsNullOrWhiteSpace(commonSettingModel.Value))
                //     {
                //         configModel = JsonConvert.DeserializeObject<OpenTelemetryConfigModel>(commonSettingModel.Value).WebConfig ?? new OpenTelemetryWebConfigModel();
                //     }
                //     else
                //     {
                //         configModel = new OpenTelemetryWebConfigModel();
                //     }
                //
                //     HttpRuntime.Cache.Insert(cacheKey, configModel, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
                // }

                //系统配置
                var commonSettingModel = Get(cacheKey, 0, secondCacheExpireMinutes: 15);
                if (commonSettingModel != null && !string.IsNullOrWhiteSpace(commonSettingModel.Value))
                {
                    configModel = JsonConvert.DeserializeObject<OpenTelemetryConfigModel>(commonSettingModel.Value).WebConfig ?? new OpenTelemetryWebConfigModel();
                }
                else
                {
                    configModel = new OpenTelemetryWebConfigModel();
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"获取前端链路的配置信息出现异常,{e}");
            }
            return configModel;
        }

        /// <summary>
        /// 获取前端链路的元素埋点配置信息
        /// </summary>
        /// <returns></returns>
        public MonitoringConfigModel GetMonitoringConfig()
        {
            MonitoringConfigModel configModel;
            try
            {
                // 测试环境不使用缓存
                var isDebug = CustomerConfig.IsDebug;
                // 查内存缓存
                var cacheKey = $"/ErpWeb/MonitoringConfig/{CustomerConfig.CloudPlatformType}/{CustomerConfig.SystemEnvironmentVersion}";
                configModel = isDebug ? null : HttpRuntime.Cache[cacheKey] as MonitoringConfigModel;
                if (configModel == null)
                {
                    // 系统配置
                    var commonSettingModel = isDebug
                        ? Get(cacheKey, 0, false)
                        : Get(cacheKey, 0);

                    if (commonSettingModel != null && !string.IsNullOrWhiteSpace(commonSettingModel.Value))
                    {
                        try
                        {
                            configModel = JsonConvert.DeserializeObject<MonitoringConfigModel>(commonSettingModel.Value);
                        }
                        catch
                        {
                            // 反序列化失败，返回默认关闭
                            configModel = new MonitoringConfigModel { IsEnabled = false };
                        }
                    }
                    else
                    {
                        configModel = new MonitoringConfigModel { IsEnabled = false };
                    }

                    HttpRuntime.Cache.Insert(cacheKey, configModel, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"获取前端链路的元素埋点配置信息出现异常,{e}");
                configModel = new MonitoringConfigModel { IsEnabled = false };
            }
            return configModel;
        }
        #region 京东推送库
        /// <summary>
        /// 京东推送库全局开关
        /// </summary>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public bool JdPushDbIsOpen(string appKey = "")
        {
            if (appKey.IsNullOrEmpty())
            {
                appKey = CustomerConfig.JingDongAppKey;
            }
            var key = "/System/JdPushDb/Global/Enable/"+ appKey;
            var config = Get(key, 0);
            // 0 未开启 1 开启
            return config != null && config.Value == "1";
        }

        /// <summary>
        /// 保存京东推送库列表
        /// </summary>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public List<PushDbInfo> GetJdPushDbList(string appKey = "")
        {
            if (appKey.IsNullOrEmpty())
            {
                appKey = CustomerConfig.JingDongAppKey;
            }
            var key = GetJdPushDbListKey(appKey);
            var config = Get<List<PushDbInfo>>(key, 0) ?? new List<PushDbInfo>();
            return config;
        }

        /// <summary>
        ///  分配京东推送库
        /// </summary>
        /// <param name="appKey"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public PushDbInfo AssignJdPushDb(int shopId)
        {
            // 检查输入参数
            if (shopId <= 0)
            {
                throw new ArgumentException("shopId 必须大于零", nameof(shopId));
            }

            var list = GetJdPushDbList();

            if (list == null || !list.Any())
            {
                throw new InvalidOperationException("PushDbInfo 列表为空或不存在");
            }

            int listCount = list.Count;

            int index = shopId % listCount;

            return list[index];
        }

        /// <summary>
        /// 设置京东推送库开通时间
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public void SetJdShopPushDbOpenTime(int shopId)
        {
            var openTimeKey = GetJdShopPushDbOpenTimeKey();
            // 推送库开通时间增加30分钟，防止订单时间差
            var currentTime = DateTime.Now.AddMinutes(30).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            Set(openTimeKey, currentTime, shopId);
        }

        /// <summary>
        /// 获取京东推送库开通时间
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public DateTime GetJdShopPushDbOpenTime(int shopId, string appKey)
        {
            var key = GetJdShopPushDbOpenTimeKey(appKey);
            var config = Get(key, shopId);
            if (config == null)
            {
                return DateTime.MinValue;
            }
            try
            {
                return DateTime.Parse(config.Value);
            }
            catch (Exception e)
            {
                Log.WriteError(e.Message + "\n" + e.StackTrace);
            }
            return DateTime.MinValue;
        }

        /// <summary>
        ///  获取店铺开通的京东推送库
        /// </summary>
        /// <param name="appKey"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public PushDbInfo GetJdShopPushDb(int shopId, string appKey = "")
        {
            var key = GetJdShopPushDbKey(appKey);
            var config = Get<PushDbInfo>(key, shopId);
            return config;
        }

        /// <summary>
        ///  更新或创建京东店铺推送库
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="pushDbInfo"></param>
        /// <returns></returns>
        public void SetJdShopPushDb(int shopId, PushDbInfo pushDbInfo)
        {
            if (shopId <= 0)
            {
                throw new ArgumentException("shopId 必须大于零", nameof(shopId));
            }
            if (pushDbInfo == null)
            {
                throw new ArgumentNullException(nameof(pushDbInfo), "pushDbInfo 不能为空");
            }

            var key = GetJdShopPushDbKey();

            var value = pushDbInfo.ToJson();

            // 更新配置
            Set(key, value, shopId);
        }

        /// <summary>
        ///  删除京东店铺推送库
        /// </summary>
        /// <param name="shopId"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void DeleteJdShopPushDb(int shopId)
        {
            Delete(GetJdShopPushDbKey(), shopId);
        }

        /// <summary>
        ///  获取京东推送库列表键,shopId为0表示默认列表
        /// </summary>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public static string GetJdPushDbListKey(string appKey)
        {
            const string jdAppKey = "/System/Jd/PushDb/DefaultList/";
            return jdAppKey + appKey;
        }

        /// <summary>
        ///  获取京东推送库列表
        /// </summary>
        /// <param name="platformPushDbName"></param>
        /// <exception cref="NotImplementedException"></exception>
        public PushDbInfo GetJdPushDbByRdsName(string platformPushDbName)
        {
            return GetJdPushDbList().Find(item => item.Rds == platformPushDbName);
        }

        /// <summary>
        ///  获取京东店铺推送库开通时间键
        /// </summary>
        /// <param name="appKey"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public static string GetJdShopPushDbOpenTimeKey(string appKey = "")
        {
            if (appKey.IsNullOrEmpty())
            {
                appKey = CustomerConfig.JingDongAppKey;
            }
            return $"/System/Jd/PushDb/OpenTime/{appKey}";
        }

        public static string GetJdShopPushDbKey(string appKey = "")
        {
            if (appKey.IsNullOrEmpty())
            {
                appKey = CustomerConfig.JingDongAppKey;
            }
            return $"/System/Jd/PushDb/{appKey}";
        }
        #endregion
        /// <summary>
        /// 获取合并子用户提醒（默认是需要提醒的）
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns>0 需要提醒，1 已处理 2 暂不处理 3 不提醒(代码检查到不需要提醒)</returns>
        public int GetMergeSubUserReminder(int shopId)
        {
            //键
            string key = "/User/Setting/MergeSubUserReminder";
            //配置
            var commonSetting = Get(key, shopId);
            //值
            var value = commonSetting?.Value ?? "0";
            //默认需要提醒
            return value.ToInt();
        }

        /// <summary>
        /// 设置合并子用户提醒
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="value">0 需要提醒，1 已处理 2 暂不处理 3 不提醒(代码检查到不需要提醒)</param>
        public void SetMergeSubUserReminder(int shopId, int value)
        {
            string key = "/User/Setting/MergeSubUserReminder";
            var commonSetting = Set(key, value.ToString(), shopId);
        }

        /// <summary>
        /// 配置库归档库链接字符串
        /// </summary>
        public string ConfigureArchiveDbConnectionString
        {
            get
            {
                return _repository.ConfigureArchiveDbConnectionString;
            }
        }

        /// <summary>
        /// 是否开启恢复过期用户归档
        /// </summary>
        public bool IsEnabledRestoreByExpiredUserArchive
        {
            get
            {
                var key = SystemSettingKeys.IsEnabledRestoreByExpiredUserArchiveKey;
                var value = _repository.GetValue(key, 0) ?? "0";
                value = string.IsNullOrWhiteSpace(value) ? "0" : value;
                return value == "1";
            }
        }

        /// <summary>
        /// 是否开启快手新的更新最后同步时间（默认：关闭）
        /// </summary>
        public bool IsEnabledKuaiShouNewUpdateLastSyncTime
        {
            get
            {
                //默认值
                const bool defaultValue = false;
                //键
                const string key = CacheKeys.IsEnabledKuaiShouNewUpdateLastSyncTimeKey;
                //是否存在二级缓存
                if (CacheHelper.Contains(key))
                {
                    try
                    {
                        return CacheHelper.Get<bool>(key);
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }
                //读取Redis开关
                try
                {
                    var value = RedisHelper.Get<bool?>(key) ?? defaultValue;
                    CacheHelper.Set(key, value, 900);
                    return value;
                }
                catch
                {
                    return defaultValue;
                }
            }
        }
         /// <summary>
        /// 是否忽略其他异常PING（默认：开启）
        /// </summary>
        /// <returns></returns>
        public bool PingIsIgnoreOtherExceptionWithCache()
        {
            return _repository.PingIsIgnoreOtherExceptionWithCache();
        }

        /// <summary>
        /// 获取头条PING接口信息，并二级缓存（15分钟）
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, Dictionary<string, string>> GetTouTiaoPingApiInfosWithCache()
        {
            return _repository.GetTouTiaoPingApiInfosWithCache();
        }

        /// <summary>
        /// 配置键非法字符
        /// </summary>
        public List<string> CommonSettingKeyIllegalCharacters
        {
            get
            {
                const string commonSettingKeyIllegalCharactersKey = CacheKeys.CommonSettingKeyIllegalCharactersKey;
                var commonSettingKeyIllegalCharacters =
                    RedisHelper.Get<List<string>>(commonSettingKeyIllegalCharactersKey);
                return commonSettingKeyIllegalCharacters;
            }
        }

        /// <summary>
        /// 获取配置键非法字符
        /// </summary>
        /// <returns></returns>
        public List<string> GetCommonSettingKeyIllegalCharactersWithCache()
        {
            //键
            const string key = CacheKeys.CommonSettingKeyIllegalCharactersKey;
            if (!CacheHelper.Contains(key))
            {
                var value = new List<string>();
                try
                {
                    value = CommonSettingKeyIllegalCharacters;
                }
                catch (Exception e)
                {
                    Log.WriteError($"获取配置键非法字符异常，配置值：{value}，异常原因：{e}",
                        $"CommonSettingKeyIllegalCharactersWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                    value = new List<string>();
                }
                if (value != null && value.Count > 0)
                    CacheHelper.Set(key, value, TimeSpan.FromMinutes(3).TotalSeconds);
            }

            var cacheValue = new List<string>();
            try
            {
                cacheValue = CacheHelper.Get<List<string>>(key);
            }
            catch (Exception e)
            {
                Log.WriteError($"从缓存获取配置键非法字符配置异常，配置值：{cacheValue}，异常原因：{e}",
                    $"CommonSettingKeyIllegalCharactersWithCacheError_{DateTime.Now:yyyy-MM-dd}.log");
                cacheValue = new List<string>();
            }
            //返回
            return cacheValue;
        }

        /// <summary>
        /// 是否开启京东订单不拆分
        /// </summary>
        public bool IsEnabledNotSplitJdOrder
        {
            get
            {
                var key = SystemSettingKeys.IsEnabledNotSplitJdOrder;
                var value = _repository.GetValue(key, 0) ?? "0";
                value = string.IsNullOrWhiteSpace(value) ? "0" : value;
                return value == "1";
            }
        }
        
        /// <summary>
        /// 是否检查引用配置信息存在
        /// </summary>
        public bool IsCheckReferenceConfigExists
        {
            get
            {
                var commonSetting = _repository.Get(SystemSettingKeys.IsCheckReferenceConfigExistsKey, 0);
                if (commonSetting == null || string.IsNullOrWhiteSpace(commonSetting.Value))
                {
                    return true;
                }

                return commonSetting.Value == "1";
            }
        }
        /// <summary>
        /// 说明：白名单启用标记的设置可在调用端按需设置，避免跨层循环依赖。
        /// 按“取号作用域 + 抖店云白名单”包装 URL，仅在取号请求内生效。
        /// 注意：该方法可用于那些直接使用 WebRequest 的场景（绕过 WebUtils/WebHelper 的地方）。
        /// </summary>
        public static string ApplyWaybillProxyIfNeeded(string url, string logisticServiceTypeName)
        {
            try
            {
                // 仅在作用域标记开启时生效
                var v = System.Runtime.Remoting.Messaging.CallContext.LogicalGetData("WaybillProxyScope.Enabled") as string;
                if (v != "1")
                    return url;

                if (CustomerConfig.CloudPlatformType != "TouTiao")
                    return url;
           
                var typeKey = "/System/Fendan/TouTiao/WhiteListTypes";
                var typeListVal = new CommonSettingService().GetString(typeKey, 0);
                if (!string.IsNullOrWhiteSpace(typeListVal))
                {
                    var serviceTypeShortName = logisticServiceTypeName.Split('.').LastOrDefault() ?? string.Empty;
                    var typeList = typeListVal.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim())
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                    var matched = typeList.Any(s => s.Equals(logisticServiceTypeName, StringComparison.OrdinalIgnoreCase)
                        || s.Equals(serviceTypeShortName, StringComparison.OrdinalIgnoreCase));
                    if (matched)
                        return CustomerConfig.GetProxyUrl(url);
                    return url;
                }
            }
            catch
            {
                return url;
            }
            return url;
        }
    }
}
