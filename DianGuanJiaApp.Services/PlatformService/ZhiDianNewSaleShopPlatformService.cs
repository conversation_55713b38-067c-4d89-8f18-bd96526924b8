using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.FxModel.InstantRetail;
using Log = DianGuanJiaApp.Utility.Log;


namespace DianGuanJiaApp.Services.PlatformService
{
    /// <summary>
    /// 抖店-即时零售
    /// </summary>
    public partial class ZhiDianNewPlatformService
    {
        #region 可合单查询
        /// <summary>
        /// 合单查询接口
        /// https://op.jinritemai.com/daojia/doc/api-docs/15/6649
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        private MergeOrderQueryResponseModel MergeOrderQueryV2(List<MergeOrderQueryRequstModel> requests)
        {
            var response = new MergeOrderQueryResponseModel();
            string jsonResponse = string.Empty;
            try
            {
                var requestData = new Dictionary<string, string>
                {
                    {
                        "merge_list",
                        requests.Select(f => new { order_id = f.OrderId, open_address_id = f.Oaid }).ToJson()
                    }
                };

                jsonResponse = _client.Execute("order.mergeV2", requestData, out _);
                Log.Debug(() => $"TouTiaoSaleShop接口order.mergeV2, 请求: {requestData.ToJson()}，响应: {jsonResponse}", "InstantRetail.txt");

                var jToken = JsonCheck(jsonResponse)?.Item1;
                if (!(jToken?.SelectToken("data.order_list") is JArray orderList)) return response;

                foreach (var item in orderList)
                {
                    var orderIds = item.Value<string>();
                    if (orderIds.Contains(","))
                        response.OrderIdsList.Add(orderIds.Split(',').ToList());
                }
            }
            catch (Exception ex)
            {
                var logFileName = $"{GetType().Name}-MergeOrderQueryV2-{DateTime.Now:yyyy-MM-dd}.txt";
                var errorMessage = ex is LogicException
                    ? ex.Message
                    : $"接口响应: {jsonResponse}, 错误信息: {ex.Message}";

                Log.WriteError(errorMessage, logFileName);
            }
            return response;
        }
        #endregion

        #region 仓库
        /// <summary>
        /// 查询仓库信息
        /// 参考打单系统处理，相同Key，仓库暂时存储在commonSetting,数据量并不会太多
        /// </summary>
        /// <returns></returns>
        public string GetWarehouseInfo()
        {
            try
            {
                var oldValue = _commonSettingService.GetString(CommonSettingService.InstantRetailWarehouseAllKey, _shop.Id) ?? string.Empty;

                var nowDate = DateTime.Now;
                DateTime lastTime;

                // 检查是否达到调用间隔
                if (!string.IsNullOrEmpty(oldValue))
                {
                    var model = oldValue.ToObject<InstantRetailWarehousesSetDto>();

                    // debug模式下, 1分钟更新一次, 正式模式下, 12小时更新一次
                    var interval = CustomerConfig.IsDebug ? TimeSpan.FromMinutes(1) : TimeSpan.FromHours(12);
                    if (nowDate - model.SetTime < interval)
                        return oldValue;

                    lastTime = GetWarehouseLastTimeApi();
                    // 如果没有更新, 则返回旧值
                    if (lastTime == model.LastTime)
                    {
                        model.SetTime = nowDate;
                        _commonSettingService.Set(CommonSettingService.InstantRetailWarehouseAllKey, model.ToJson(), _shop.Id);
                        return oldValue;
                    }
                }
                else
                {
                    lastTime = GetWarehouseLastTimeApi();
                }

                // 从api获取信息
                var list = GetWarehouseApi();
                if (list.IsNullOrEmpty()) return oldValue;
                var value = new InstantRetailWarehousesSetDto(list.Select(x => new InstantRetailWarehousesDto()
                {
                    Id = x.out_warehouse_id,
                    Name = x.name
                }).ToList(), lastTime).ToJson();

                _commonSettingService.Set(CommonSettingService.InstantRetailWarehouseAllKey, value, _shop.Id);
                return value;
            }
            catch (Exception e)
            {
                Log.WriteError($"【{_shop.ShopId}】获取门店列表信息失败，异常信息：{e}");
                return null;
            }
        }

        /// <summary>
        /// 获取仓库最新更新时间api
        /// </summary>
        /// <returns></returns>
        public DateTime GetWarehouseLastTimeApi()
        {
            var dict = new Dictionary<string, string> { { "order_by", "update_time" }, { "rank", "desc" }, { "page", "0" }, { "size", "1" } };
            var json = _client.Execute("warehouse.list", dict, out _);
            var tuple = JsonCheck2(json);
            if (tuple.Item2 != 10000) return DateTime.Now;

            var lastTime = tuple.Item1["data"]?["warehouses"]?[0]?["update_time"]?.ToLong() ?? 0;
            return lastTime != 0 ? DateConverter.ConvertTicksToDateTime(lastTime) : DateTime.Now;
        }

        /// <summary>
        /// 获取仓库列表信息
        /// </summary>
        /// <returns></returns>
        public List<InstantRetailWarehousesALLDto> GetWarehouseApi()
        {
            var result = new List<InstantRetailWarehousesALLDto>();
            var dict = new Dictionary<string, string> { { "order_by", "update_time" }, { "rank", "desc" }, { "page", "0" }, { "size", "100" } };
            // 接口文档: https://op.jinritemai.com/daojia/doc/api-docs/34/1858
            const string apiUrl = "warehouse.list";

            do
            {
                var json = _client.Execute(apiUrl, dict, out _);
                var tuple = JsonCheck2(json);
                if (tuple.Item2 != 10000) break;

                var data = tuple.Item1.Value<JToken>("data");
                var dtoList = GetWarehouseFromJson(data);
                if (dtoList == null || !dtoList.Any()) break;
                result.AddRange(dtoList);

                if (data.Value<long>("total") <= result.Count) break;
                dict["page"] = (dict["page"].ToInt() + 1).ToString();
            } while (true);

            return result;
        }

        /// <summary>
        /// 获取仓库模型dto
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static List<InstantRetailWarehousesALLDto> GetWarehouseFromJson(JToken data)
        {
            return data?.Value<JArray>("warehouses")?
                .Select(item =>
                {
                    var model = new InstantRetailWarehousesALLDto();
                    model.name = item.Value<string>("name");
                    model.out_warehouse_id = item.Value<string>("out_warehouse_id");
                    model.warehouse_id = item.Value<long>("warehouse_id");
                    model.address_detail = item.Value<string>("address_detail");
                    model.create_time = item.Value<long>("create_time");
                    model.intro = item.Value<string>("intro");
                    model.update_time = item.Value<long>("update_time");
                    model.shop_id = item.Value<long>("shop_id");
                    model.warehouse_location = item.Value<JToken>("warehouse_location")?.ToString()?.ToObject<InstantRetailWarehousesALLDto.Warehouse_Location>();
                    model.addr = item.Value<JToken>("addr")?.ToString()?.ToObject<List<InstantRetailWarehousesALLDto.Addr>>();
                    model.out_fence_ids = item.Value<JToken>("out_fence_ids")?.ToString()?.ToObject<List<string>>();
                    return model;
                })?.ToList() ?? new List<InstantRetailWarehousesALLDto>();
        }
        #endregion

        #region 私方法
        
        /// <summary>
        /// ToDecimal
        /// </summary>
        /// <param name="value"></param>
        /// <param name="decimalPlaces">小数位数</param>
        /// <returns></returns>
        public decimal ToDecimal(long value, int decimalPlaces = 2)
        {
            if (value == 0)
                return value;
            if (decimalPlaces < 0)
                decimalPlaces = 2;

            var divisor = (decimal)Math.Pow(10, decimalPlaces);
            
            return value / divisor;
        }

        /// <summary>
        /// 获取当前时间(十位时间戳)
        /// </summary>
        /// <returns></returns>
        private string GetCurrentTimestamp()
        {
            var now = DateTimeOffset.UtcNow;

            var timestamp = now.ToUnixTimeSeconds();

            return timestamp.ToString();
        }
        #endregion
    }
}
