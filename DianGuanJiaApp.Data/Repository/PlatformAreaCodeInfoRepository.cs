using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Extension;
using System.Web;
using DianGuanJiaApp.Utility;
using System.Web.UI.WebControls;

namespace DianGuanJiaApp.Data.Repository
{
    public partial class PlatformAreaCodeInfoRepository : BaseRepository<PlatformAreaCodeInfo>
    {
        public PlatformAreaCodeInfoRepository() : base(Utility.CustomerConfig.ConfigureDbConnectionString)
        {
        }

        /// <summary>
        /// 获取指定平台的省市区保存到一个List中
        /// </summary>
        /// <param name="platformType">指定的平台</param>
        /// <returns></returns>
        public List<PlatformProvinceModel> GetTreeAreaInfoList(string platformType, bool needSubfix = false)
        {
            string sql = @"SELECT * FROM PlatformAreaCodeInfo a1 WITH(NOLOCK)
                           JOIN PlatformAreaCodeInfo a2 WITH(NOLOCK) ON a1.UniqueKey = a2.ParentUniqueKey
                           JOIN PlatformAreaCodeInfo a3 WITH(NOLOCK) ON a2.UniqueKey = a3.ParentUniqueKey
                           WHERE a1.ParentUniqueKey='' AND a1.PlatformType=@platformType
                           ORDER BY a1.Name";

            var provinces = new List<PlatformProvinceModel>();
            var provinceKey = CommUtls.ProvinceKeys();
            var cityKey = CommUtls.CityKeys();
            var areaKey = CommUtls.AreaKeys();
            var areanInfos = DbConnection.Query<PlatformAreaCodeInfo, PlatformAreaCodeInfo, PlatformAreaCodeInfo, List<PlatformProvinceModel>>
                (sql, (a1, a2, a3) =>
                {
                    PlatformProvinceModel province = provinces.FirstOrDefault(m => m.ProvinceInfo.Id == a1.Id);
                    if (province == null)
                    {
                        if (!needSubfix)
                            a1.Name = a1.Name.TrimEndOnce(provinceKey);
                        //if (a1.Name == "广西壮族自治区") a1.Name = "广西";
                        //else if (a1.Name == "内蒙古自治区") a1.Name = "内蒙古";
                        //else if (a1.Name == "宁夏回族自治区") a1.Name = "宁夏";
                        //else if (a1.Name == "西藏自治区") a1.Name = "西藏";
                        //else if (a1.Name == "香港特别行政区") a1.Name = "香港";
                        //else if (a1.Name == "新疆维吾尔自治区") a1.Name = "新疆";
                        province = new PlatformProvinceModel() { ProvinceInfo = a1 };
                        provinces.Add(province);
                    }

                    PlatformCityModel city = province.CityLst.FirstOrDefault(m => m.CityInfo.Id == a2.Id);
                    if (a2 != null && city == null)
                    {
                        if (!needSubfix)
                            a2.Name = a2.Name.TrimEndOnce(cityKey);
                        city = new PlatformCityModel() { CityInfo = a2 };
                        province.CityLst.Add(city);
                    }

                    if (a3 != null && city.AreaLst.Count(m => m.Id == a3.Id) == 0)
                    {
                        if (!needSubfix && a3.Name.Length > 2) // 就2个字的区名称，不用去除后缀，如盂县，去除后只有 '盂'，数据不完整
                            a3.Name = a3.Name.TrimEndOnce(areaKey);
                        city.AreaLst.Add(a3);
                    }

                    return provinces;
                }, new { platformType = platformType });
            return provinces;
        }

        /// <summary>
        /// 获取指定平台的省市区保存到一个List中
        /// </summary>
        /// <param name="platformType">指定的平台</param>
        /// <param name="needSubfix">是否需要剔除后缀</param>
        /// <returns></returns>
        public List<PlatformProvinceModel> GetTreeAreaInfos(string platformType, bool needSubfix = false)
        {
            var key = $"/System/Config/PlatformTreeAreas/{platformType}/{needSubfix}";
            var cache = HttpRuntime.Cache[key]?.ExtToString()?.ToObject<List<PlatformProvinceModel>>();
            if (cache == null || !cache.Any())
            {
                cache = GetTreeAreaInfoList(platformType, needSubfix);
                HttpRuntime.Cache.Insert(key, cache.ToJson(), null, DateTime.Now.AddHours(1), System.Web.Caching.Cache.NoSlidingExpiration);
            }
            return cache;
        }

        /// <summary>
        /// 根据uniqueKeys列表查询
        /// </summary>
        /// <param name="uniqueKeys"></param>
        /// <param name="fields">字段</param>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetListByUniqueKey(List<string> uniqueKeys, List<string> fields = null)
        {
            var strFields = "*";
            if (fields != null && fields.Any())
                strFields = string.Join(",", fields);
            var sql = $"SELECT {strFields} FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE UniqueKey IN @keys ";

            var result = new List<PlatformAreaCodeInfo>();
            var index = 0;
            while (true)
            {
                var curCodes = uniqueKeys.Skip(index * 500).Take(500).ToList();
                index++;
                if (curCodes == null || curCodes.Any() == false)
                    break;
                var parameters = new DynamicParameters();
                parameters.Add("keys", curCodes);
                var temps = DbConnection.Query<PlatformAreaCodeInfo>(sql, parameters)?.ToList();
                if (temps != null && temps.Any())
                    result.AddRange(temps);
            }
            return result;
        }

        /// <summary>
        /// 根据ParentUniqueKey查询
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="parentKey">字段</param>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetListByParentUniqueKey(string platformType, string parentKey)
        {
            var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE ParentUniqueKey=@parentKey AND PlatformType=@platformType ";
            return DbConnection.Query<PlatformAreaCodeInfo>(sql, new { parentKey = parentKey, platformType = platformType }).ToList();
        }

        /// <summary>
        /// 根据platformType查询
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<PlatformAreaCodeInfo> GetListByPlatformType(string platformType)
        {
            var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE PlatformType=@platformType ";
            return DbConnection.Query<PlatformAreaCodeInfo>(sql, new { platformType = platformType }).ToList();
        }

        /// <summary>
        /// 获取平台的售后地址
        /// </summary>
        public void GetPtAddressCode(Dictionary<string, string> dic, string province, string city, string county, string street, string platformType)
        {
            //var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE PlatformType=@platformType and Name = @streetName ";
            //var streetInfo = DbConnection.QueryFirstOrDefault<PlatformAreaCodeInfo>(sql, new { platformType, streetName });
            //if (streetInfo != null)
            //{
            //    dic[streetName] = streetInfo?.Code;
            //    if (!string.IsNullOrWhiteSpace(streetInfo?.ParentUniqueKey))
            //        GetAddressByParent(dic, streetInfo?.ParentUniqueKey, platformType);
            //}

            //var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE PlatformType=@platformType and Name = @street ";
            //var streetInfo = DbConnection.QueryFirstOrDefault<PlatformAreaCodeInfo>(sql, new { platformType, street });
            //if (streetInfo != null)
            //{
            //    dic[street] = streetInfo?.Code;
            //    if (!string.IsNullOrWhiteSpace(streetInfo?.ParentUniqueKey))
            //    {
            //        var countyInfo = GetAddressByParent(dic, streetInfo?.ParentUniqueKey, platformType, county);
            //        if (countyInfo != null && !string.IsNullOrWhiteSpace(countyInfo?.ParentUniqueKey))
            //        {
            //            var cityInfo = GetAddressByParent(dic, countyInfo.ParentUniqueKey, platformType, city);
            //            if (cityInfo != null && !string.IsNullOrWhiteSpace(cityInfo?.ParentUniqueKey))
            //            {
            //                GetAddressByParent(dic, cityInfo.ParentUniqueKey, platformType, province);
            //            }
            //        }
            //    }
            //}

            var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE PlatformType=@platformType and Name in @name ";
            var areaList = DbConnection.Query<PlatformAreaCodeInfo>(sql, new { platformType, name = new string[] { province, city, county, street } }).ToList();
            var provinceInfo = areaList.Find(x => x.Name == province);
            if (provinceInfo != null)
            {
                dic[province] = provinceInfo?.Code;
                var cityInfo = areaList.Find(x => x.Name == city && x.ParentUniqueKey == provinceInfo.UniqueKey);
                if (cityInfo != null)
                {
                    dic[city] = cityInfo?.Code;
                    var countyInfo = areaList.Find(x => x.Name == county && x.ParentUniqueKey == cityInfo.UniqueKey);
                    if (countyInfo != null)
                    {
                        dic[county] = countyInfo?.Code;
                        var streetInfo = areaList.Find(x => x.Name == street && x.ParentUniqueKey == countyInfo.UniqueKey);
                        if (streetInfo != null)
                        {
                            dic[street] = streetInfo?.Code;
                        }
                    }
                }
            }
        }

        public PlatformAreaCodeInfo PlatformAreaByCode(long code, string platformType)
        {
            var sql = $"SELECT * FROM PlatformAreaCodeInfo WITH(NOLOCK) WHERE PlatformType = @platformType and Code = @code";
            return DbConnection.QueryFirstOrDefault<PlatformAreaCodeInfo>(sql, new { platformType, code });
        }
    }
}
