using System;
using System.Collections.Generic;

namespace DianGuanJiaApp.Data.FxModel.InstantRetail
{
    /// <summary>
    /// 即时零售 仓库列表模型
    /// </summary>
    public class InstantRetailWarehousesDto
    {
        /// <summary>
        /// 仓库Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string Name { get; set; }
    }

    public class InstantRetailWarehousesALLDto
    {
        /// <summary>
        /// 仓库介绍
        /// </summary>
        public string intro { get; set; }

        /// <summary>
        /// 仓库覆盖范围列表
        /// </summary>
        public List<Addr> addr { get; set; }

        /// <summary>
        /// 仓的地址编码
        /// </summary>
        public Warehouse_Location warehouse_location { get; set; }

        /// <summary>
        /// 仓的详细地址
        /// </summary>
        public string address_detail { get; set; }

        /// <summary>
        /// 仓库id
        /// </summary>
        public long warehouse_id { get; set; }


        public long update_time { get; set; }

        public long create_time { get; set; }

        /// <summary>
        /// 店铺id
        /// </summary>
        public long shop_id { get; set; }

        /// <summary>
        /// 仓库外部id
        /// </summary>
        public string out_warehouse_id { get; set; }

        /// <summary>
        /// 仓库名字
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 外部电子围栏id列表
        /// </summary>
        public List<string> out_fence_ids { get; set; }

        public class Warehouse_Location
        {
            /// <summary>
            /// 省地址编码
            /// </summary>
            public long address_id1 { get; set; }

            /// <summary>
            /// 市地址编码
            /// </summary>
            public long address_id2 { get; set; }

            /// <summary>
            /// 区地址编码
            /// </summary>
            public long address_id3 { get; set; }

            /// <summary>
            /// 街道地址编码
            /// </summary>
            public long address_id4 { get; set; }
        }

        public class Addr
        {
            public long addr_id1 { get; set; }
            public long addr_id2 { get; set; }
            public long addr_id3 { get; set; }
            public long addr_id4 { get; set; }
            public long update_time { get; set; }
            public long create_time { get; set; }
        }
    }

    /// <summary>
    /// 即时零售 仓库列表更新模型
    /// </summary>
    public class InstantRetailWarehousesSetDto
    {
        /// <summary>
        /// 仓库数据
        /// </summary>
        public List<InstantRetailWarehousesDto> List { get; set; }

        /// <summary>
        /// 上次更新时间
        /// </summary>
        public DateTime SetTime { get; set; }

        /// <summary>
        /// 最新数据时间
        /// </summary>
        public DateTime LastTime { get; set; }

        /// <summary>
        /// 即时零售 仓库列表更新模型
        /// </summary>
        public InstantRetailWarehousesSetDto()
        {
        }

        /// <summary>
        /// 即时零售 仓库列表更新模型
        /// </summary>
        public InstantRetailWarehousesSetDto(List<InstantRetailWarehousesDto> list, DateTime lastTime)
        {
            List = list;
            LastTime = lastTime;
            SetTime = DateTime.Now;
        }
    }
}