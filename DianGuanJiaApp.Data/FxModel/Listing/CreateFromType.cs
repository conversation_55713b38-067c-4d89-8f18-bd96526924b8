namespace DianGuanJiaApp.Data.FxModel.Listing
{
    /// <summary>
    /// 铺货来源类型 2025-09-02 整理成类型
    /// </summary>
    public struct CreateFromType
    {
        /// <summary>
        /// 自己的基础商品
        /// </summary>
        public const string SelfBaseProduct = "SelfBaseProduct";

        /// <summary>
        /// 自己的货盘 | 我的小站
        /// </summary>
        public const string SelfSupplierProduct = "SelfSupplierProduct";

        /// <summary>
        /// 厂家货盘 | 厂家的小站
        /// </summary>
        public const string SupplierProduct = "SupplierProduct";

        /// <summary>
        /// 厂家平台资料
        /// </summary>
        public const string SupplierPtProduct = "SupplierPtProduct";

        /// <summary>
        /// 厂家-即时零售货源资料 新增类型：2025-09-02
        /// </summary>
        public const string SupplierProductSaleShop = "SupplierProductSaleShop";
    }
}
